pluginManagement {
    repositories {
        **ARTIFACTORYREPOSITORY**
        gradlePluginPortal()
        google()
        mavenCentral()
    }
}

include ':launcher', ':unityLibrary'
**INCLUDES**

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_SETTINGS)
    repositories {
        **ARTIFACTORYREPOSITORY**
        google()
        mavenCentral()
// Android Resolver Repos Start
        def unityProjectPath = $/file:///**DIR_UNITYPROJECT**/$.replace("\\", "/")
        maven {
            url "https://repo.maven.apache.org/maven2/" // Assets/GoogleMobileAds/Mediation/MetaAudienceNetwork/Editor/MetaAudienceNetworkMediationDependencies.xml:24, Assets/GoogleMobileAds/Mediation/Pangle/Editor/PangleMediationDependencies.xml:25, Assets/LevelPlay/Editor/ISFyberAdapterDependencies.xml:9
        }
        maven {
            url "https://maven.google.com/" // Assets/LevelPlay/Editor/IronSourceSDKDependencies.xml:12, Packages/com.dmobin.google.ads.mobile/GoogleMobileAds/Editor/GoogleMobileAdsDependencies.xml:7, Assets/LevelPlay/Editor/ISMintegralAdapterDependencies.xml:16, Assets/LevelPlay/Editor/ISUnityAdsAdapterDependencies.xml:13, Packages/com.dmobin.yandexmobileads.lite/Editor/YandexMobileadsDependencies.xml:7, Packages/com.dmobin.sdk.mediation.googlemobileads-native/GoogleMobileAdsNative/Editor/GoogleMobileAdsNativeDependencies.xml:7, Packages/com.dmobin.google.ads.mobile/GoogleMobileAds/Editor/GoogleMobileAdsDependencies.xml:12, Packages/com.dmobin.google.ads.mobile/GoogleMobileAds/Editor/GoogleUmpDependencies.xml:7, Packages/com.dmobin.yandexmobileads.lite/Editor/YandexMobileadsDependencies.xml:12
        }
        maven {
            url "https://artifactory.bidmachine.io/bidmachine" // Assets/LevelPlay/Editor/ISBidMachineAdapterDependencies.xml:12
        }
        maven {
            url "https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea/" // Assets/LevelPlay/Editor/ISMintegralAdapterDependencies.xml:9
        }
        maven {
            url "https://artifact.bytedance.com/repository/pangle/" // Assets/LevelPlay/Editor/ISPangleAdapterDependencies.xml:13, Assets/GoogleMobileAds/Mediation/Pangle/Editor/PangleMediationDependencies.xml:25
        }
        maven {
            url "https://jitpack.io/" // Assets/LevelPlay/Editor/ISVungleAdapterDependencies.xml:9
        }
        maven {
            url "https://maven.google.com" // Packages/com.dmobin.adjust/Native/Editor/AndroidDependencies.xml:20, Packages/com.dmobin.adjust/Native/Editor/AndroidDependencies.xml:29
        }
        maven {
            url "https://asia-southeast1-maven.pkg.dev/knorex-rtb/ascendx-sdk" // Packages/com.dmobin.costcenter.mediation.s2sbidding/S2sBiddingSdk/Editor/Dependencies.xml:8, Packages/com.dmobin.costcenter.mediation.s2sbidding/S2sPluginRenderers/Editor/Dependencies.xml:8
        }
        maven {
            url (unityProjectPath + "/Assets/GeneratedLocalRepo/Firebase/m2repository") // Packages/com.dmobin.google.firebase.analytics/Firebase/Editor/AnalyticsDependencies.xml:18, Packages/com.dmobin.google.firebase.app/Firebase/Editor/AppDependencies.xml:21, Packages/com.dmobin.google.firebase.messaging/Firebase/Editor/MessagingDependencies.xml:24, Packages/com.dmobin.google.firebase.crashlytics/Firebase/Editor/CrashlyticsDependencies.xml:20, Packages/com.dmobin.google.firebase.remote-config/Firebase/Editor/RemoteConfigDependencies.xml:20
        }
        maven {
            url "https://dl.google.com/dl/android/maven2/" // Assets/GoogleMobileAds/Mediation/MetaAudienceNetwork/Editor/MetaAudienceNetworkMediationDependencies.xml:24, Assets/GoogleMobileAds/Mediation/Pangle/Editor/PangleMediationDependencies.xml:25
        }
        mavenLocal()
// Android Resolver Repos End
        flatDir {
            dirs "${project(':unityLibrary').projectDir}/libs"
        }
    }
}
