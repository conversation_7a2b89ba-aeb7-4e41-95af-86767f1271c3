# Native Ad Controller - Multi-View Support

## Overview

The updated NativeAdController now supports displaying multiple NativeAdView instances simultaneously with improved ad management and event tracking.

## Key Features

### 1. Flexible Ad Loading Strategy
- **LoadMultipleAds**: Boolean flag to control loading behavior
  - `true`: Load multiple ads equal to the number of adUnitIds
  - `false`: Load only 1 ad and rotate through adUnitIds
- **Smart Pool Management**: Automatically manages ad pool based on LoadMultipleAds setting
- **Pool Replenishment**: Automatically loads new ads when needed

### 2. Location-Based Ad Assignment
- Each NativeAdView has a unique `location` identifier
- Ads are assigned to specific locations and tracked individually
- Automatic location generation if not manually set

### 3. Enhanced Event System
- **OnAdPaid(AdValue, string location)**: Triggered when an ad generates revenue, includes location
- **OnAdShow(string location)**: Triggered when an ad view becomes visible
- **OnAdHide(string location)**: Triggered when an ad view becomes hidden
- **OnAdLoaded(NativeAd)**: Triggered when a new ad is loaded into the pool
- **OnAdFailedToLoad(string error)**: Triggered when ad loading fails

## Usage

### 1. Setup NativeAdController

```csharp
// Initialize with multiple ad unit IDs
string[] adUnitIds = {
    "your-ad-unit-id-1",
    "your-ad-unit-id-2",
    "your-ad-unit-id-3"
};

// Configure loading behavior
NativeAdController.Instance.LoadMultipleAds = true; // Load multiple ads (equal to adUnitIds count)
// OR
NativeAdController.Instance.LoadMultipleAds = false; // Load only 1 ad and rotate adUnitIds

NativeAdController.Instance.Init(adUnitIds);
```

### 2. Setup NativeAdView

In the Inspector:
- Set `Location` field to a unique identifier (e.g., "main_menu", "game_over", "shop")
- If left empty, it will auto-generate based on GameObject name and instance ID

### 3. Subscribe to Events

```csharp
NativeAdController.Instance.OnAdPaid += (adValue, location) => {
    Debug.Log($"Ad revenue: {adValue.Value} at {location}");
};

NativeAdController.Instance.OnAdShow += (location) => {
    Debug.Log($"Ad shown at {location}");
};

NativeAdController.Instance.OnAdHide += (location) => {
    Debug.Log($"Ad hidden at {location}");
};
```

## How It Works

### Ad Loading Process

**When LoadMultipleAds = true:**
1. **Initialization**: Controller loads ads equal to adUnitIds count
2. **Pool Management**: Maintains a queue of loaded ads ready for assignment
3. **Assignment**: When a NativeAdView needs an ad, it gets one from the pool
4. **Replenishment**: New ads are loaded to maintain the pool size

**When LoadMultipleAds = false:**
1. **Initialization**: Controller loads only 1 ad using rotation of adUnitIds
2. **Shared Usage**: Single ad is shared among all NativeAdViews
3. **Rotation**: AdUnitIds are rotated for each new ad request
4. **Efficient Memory**: Uses minimal memory with single ad instance

### Ad Assignment Process
1. **View Initialization**: NativeAdView requests an ad for its location
2. **Pool Check**: Controller checks if an ad is available in the pool
3. **Assignment**: Ad is moved from pool to active ads dictionary with location key
4. **Event Setup**: OnPaidEvent is configured to include location information

### Event Flow
1. **Show/Hide**: Triggered when NativeAdView.ShowContent() or HideContent() is called
2. **Paid Events**: Triggered when the assigned ad generates revenue
3. **Location Tracking**: All events include the location identifier for precise tracking

## Best Practices

1. **Unique Locations**: Ensure each NativeAdView has a unique location identifier
2. **Loading Strategy**: Choose LoadMultipleAds based on your needs:
   - `true`: For multiple different ads simultaneously (higher memory usage)
   - `false`: For shared single ad across views (lower memory usage)
3. **Event Handling**: Always unsubscribe from events in OnDestroy to prevent memory leaks
4. **Testing**: Use test ad unit IDs during development

## Migration from Previous Version

The new system is backward compatible, but to take advantage of new features:

1. Add `location` field to your NativeAdView prefabs
2. Update event subscriptions to handle the new location parameter
3. Set `LoadMultipleAds` based on your requirements
4. Update any custom ad management logic to work with the new pool system
