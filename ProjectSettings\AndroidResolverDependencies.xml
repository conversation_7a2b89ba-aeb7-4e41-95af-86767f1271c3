<dependencies>
  <packages>
    <package>androidx.constraintlayout:constraintlayout:2.1.4</package>
    <package>androidx.lifecycle:lifecycle-process:2.4.1</package>
    <package>androidx.recyclerview:recyclerview:1.2.1</package>
    <package>com.adjust.sdk:adjust-android:5.1.0</package>
    <package>com.android.installreferrer:installreferrer:2.2</package>
    <package>com.android.support:appcompat-v7:25.3.1</package>
    <package>com.android.support:cardview-v7:25.3.1</package>
    <package>com.android.support:customtabs:25.3.1</package>
    <package>com.android.support:support-v4:25.3.1</package>
    <package>com.applovin:applovin-sdk:13.0.0</package>
    <package>com.bigossp:bigo-ads:5.0.2</package>
    <package>com.facebook.android:audience-network-sdk:6.18.0</package>
    <package>com.facebook.android:facebook-applinks:[18.0.0,19)</package>
    <package>com.facebook.android:facebook-core:[18.0.0,19)</package>
    <package>com.facebook.android:facebook-gamingservices:[18.0.0,19)</package>
    <package>com.facebook.android:facebook-login:[18.0.0,19)</package>
    <package>com.facebook.android:facebook-share:[18.0.0,19)</package>
    <package>com.fyber:marketplace-sdk:8.3.5</package>
    <package>com.github.bumptech.glide:glide:4.16.0</package>
    <package>com.google.ads.mediation:facebook:6.18.0.0</package>
    <package>com.google.ads.mediation:pangle:6.4.0.6.0</package>
    <package>com.google.android.gms:play-services-ads:23.6.0</package>
    <package>com.google.android.gms:play-services-ads-identifier:18.0.1</package>
    <package>com.google.android.gms:play-services-ads-identifier:18.1.0</package>
    <package>com.google.android.gms:play-services-appset:16.0.2</package>
    <package>com.google.android.gms:play-services-base:18.5.0</package>
    <package>com.google.android.play:core-common:2.0.4</package>
    <package>com.google.android.play:review:2.0.2</package>
    <package>com.google.android.ump:user-messaging-platform:3.1.0</package>
    <package>com.google.code.gson:gson:2.8.5</package>
    <package>com.google.firebase:firebase-analytics:22.1.2</package>
    <package>com.google.firebase:firebase-analytics-unity:12.5.0</package>
    <package>com.google.firebase:firebase-app-unity:12.5.0</package>
    <package>com.google.firebase:firebase-common:21.0.0</package>
    <package>com.google.firebase:firebase-config:22.0.1</package>
    <package>com.google.firebase:firebase-config-unity:12.5.0</package>
    <package>com.google.firebase:firebase-crashlytics-ndk:19.3.0</package>
    <package>com.google.firebase:firebase-crashlytics-unity:12.5.0</package>
    <package>com.google.firebase:firebase-iid:21.1.0</package>
    <package>com.google.firebase:firebase-messaging:24.1.0</package>
    <package>com.google.firebase:firebase-messaging-unity:12.5.0</package>
    <package>com.google.flatbuffers:flatbuffers-java:1.12.0</package>
    <package>com.knorex:knorex-sdk-plugin-renders:1.6.0</package>
    <package>com.knorex:knorex-sdk-unity:1.6.0</package>
    <package>com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.31</package>
    <package>com.moloco.sdk:moloco-sdk:3.6.1</package>
    <package>com.my.target:mytarget-sdk:5.27.1</package>
    <package>com.pangle.global:ads-sdk:6.4.0.6</package>
    <package>com.parse.bolts:bolts-android:1.4.0</package>
    <package>com.unity3d.ads:unity-ads:4.13.1</package>
    <package>com.unity3d.ads-mediation:admob-adapter:4.3.49</package>
    <package>com.unity3d.ads-mediation:adquality-sdk:7.23.2</package>
    <package>com.unity3d.ads-mediation:adquality-unity-sdk-bridge:7.23.2</package>
    <package>com.unity3d.ads-mediation:applovin-adapter:4.3.47</package>
    <package>com.unity3d.ads-mediation:bidmachine-adapter:4.3.14</package>
    <package>com.unity3d.ads-mediation:bigo-adapter:4.3.5</package>
    <package>com.unity3d.ads-mediation:facebook-adapter:4.3.48</package>
    <package>com.unity3d.ads-mediation:fyber-adapter:4.3.36</package>
    <package>com.unity3d.ads-mediation:mediation-sdk:8.6.1</package>
    <package>com.unity3d.ads-mediation:mintegral-adapter:4.3.35</package>
    <package>com.unity3d.ads-mediation:moloco-adapter:4.3.12</package>
    <package>com.unity3d.ads-mediation:mytarget-adapter:4.3.0</package>
    <package>com.unity3d.ads-mediation:pangle-adapter:4.3.34</package>
    <package>com.unity3d.ads-mediation:unityads-adapter:4.3.49</package>
    <package>com.unity3d.ads-mediation:vungle-adapter:4.3.29</package>
    <package>com.unity3d.ads-mediation:yandex-adapter:4.3.7</package>
    <package>com.vungle:vungle-ads:7.4.3</package>
    <package>com.yandex.android:mobileads:7.8.0</package>
    <package>io.bidmachine:ads:3.3.0</package>
    <package>jp.wasabeef:blurry:4.0.1</package>
  </packages>
  <files>
    <file>Assets/GeneratedLocalRepo/Firebase/m2repository/com/google/firebase/firebase-analytics-unity/12.5.0/firebase-analytics-unity-12.5.0.aar</file>
    <file>Assets/GeneratedLocalRepo/Firebase/m2repository/com/google/firebase/firebase-analytics-unity/12.5.0/firebase-analytics-unity-12.5.0.pom</file>
    <file>Assets/GeneratedLocalRepo/Firebase/m2repository/com/google/firebase/firebase-app-unity/12.5.0/firebase-app-unity-12.5.0.aar</file>
    <file>Assets/GeneratedLocalRepo/Firebase/m2repository/com/google/firebase/firebase-app-unity/12.5.0/firebase-app-unity-12.5.0.pom</file>
    <file>Assets/GeneratedLocalRepo/Firebase/m2repository/com/google/firebase/firebase-config-unity/12.5.0/firebase-config-unity-12.5.0.aar</file>
    <file>Assets/GeneratedLocalRepo/Firebase/m2repository/com/google/firebase/firebase-config-unity/12.5.0/firebase-config-unity-12.5.0.pom</file>
    <file>Assets/GeneratedLocalRepo/Firebase/m2repository/com/google/firebase/firebase-crashlytics-unity/12.5.0/firebase-crashlytics-unity-12.5.0.aar</file>
    <file>Assets/GeneratedLocalRepo/Firebase/m2repository/com/google/firebase/firebase-crashlytics-unity/12.5.0/firebase-crashlytics-unity-12.5.0.pom</file>
    <file>Assets/GeneratedLocalRepo/Firebase/m2repository/com/google/firebase/firebase-messaging-unity/12.5.0/firebase-messaging-unity-12.5.0.aar</file>
    <file>Assets/GeneratedLocalRepo/Firebase/m2repository/com/google/firebase/firebase-messaging-unity/12.5.0/firebase-messaging-unity-12.5.0.pom</file>
  </files>
  <settings>
    <setting name="androidAbis" value="arm64-v8a,armeabi-v7a" />
    <setting name="bundleId" value="com.dmobin.gdk" />
    <setting name="explodeAars" value="True" />
    <setting name="gradleBuildEnabled" value="True" />
    <setting name="gradlePropertiesTemplateEnabled" value="True" />
    <setting name="gradleTemplateEnabled" value="True" />
    <setting name="installAndroidPackages" value="True" />
    <setting name="localMavenRepoDir" value="Assets/GeneratedLocalRepo" />
    <setting name="packageDir" value="Assets/Plugins/Android" />
    <setting name="patchAndroidManifest" value="True" />
    <setting name="patchMainTemplateGradle" value="True" />
    <setting name="projectExportEnabled" value="False" />
    <setting name="useFullCustomMavenRepoPathWhenExport" value="True" />
    <setting name="useFullCustomMavenRepoPathWhenNotExport" value="False" />
    <setting name="useJetifier" value="True" />
  </settings>
</dependencies>