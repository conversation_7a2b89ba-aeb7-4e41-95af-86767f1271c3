#if GDK_USE_ADMOB
using GoogleMobileAds.Api;
#endif
using DSDK.Core;
using DSDK.Logger;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using DSDK.Mediation;
using DSDK.Remote;
using System.Collections;
using DSDK.Data;
using DSDK.AnalyticsPlatform;
using System;

public class AdDemo : MonoBehaviour
{
    [SerializeField] TMP_Text text;
    [SerializeField] TMP_Dropdown networkDropdown;
    [SerializeField] Button bannerButton;
    [SerializeField] Button bannerTopButton;
    [SerializeField] Button bannerHideButton;
    [SerializeField] Button bannerTopHideButton;
    [SerializeField] Button interstitialButton;
    [SerializeField] Button rewardedVideoButton;
    [SerializeField] Button nativeBannerButton;
    [SerializeField] Button nativeBannerHideButton;
    [SerializeField] Button nativeFullscreenButton;
    [SerializeField] Button deviceIdButton;
    [SerializeField] Button expandNativeBannerButton;
    private MediationType _currentNetwork = MediationType.ironSource;

    private void Awake()
    {
        // Get Text of all button and set to text
        bannerButton.GetComponentInChildren<TMP_Text>().text = "Banner";
        bannerTopButton.GetComponentInChildren<TMP_Text>().text = "BannerTop";
        bannerHideButton.GetComponentInChildren<TMP_Text>().text = "BannerHide";
        bannerTopHideButton.GetComponentInChildren<TMP_Text>().text = "BannerTopHide";
        interstitialButton.GetComponentInChildren<TMP_Text>().text = "Interstitial";
        rewardedVideoButton.GetComponentInChildren<TMP_Text>().text = "RewardedVideo";
        nativeBannerButton.GetComponentInChildren<TMP_Text>().text = "Extend";
        nativeBannerHideButton.GetComponentInChildren<TMP_Text>().text = "Collase";
        nativeFullscreenButton.GetComponentInChildren<TMP_Text>().text = "NativeFullscreen";
        deviceIdButton.GetComponentInChildren<TMP_Text>().text = "DeviceId";
        expandNativeBannerButton.GetComponentInChildren<TMP_Text>().text = "ExpandNativeBanner";
    }

    private void OnEnable()
    {
        networkDropdown.onValueChanged.AddListener(OnNetworkChanged);
        bannerButton.onClick.AddListener(ShowBanner);
        bannerTopButton.onClick.AddListener(ShowBannerTop);
        bannerHideButton.onClick.AddListener(HideBanner);
        bannerTopHideButton.onClick.AddListener(HideBannerTop);
        interstitialButton.onClick.AddListener(ShowInterstitial);
        rewardedVideoButton.onClick.AddListener(ShowRewardedVideo);
        nativeBannerButton.onClick.AddListener(Extend);
        nativeBannerHideButton.onClick.AddListener(Collase);
        nativeFullscreenButton.onClick.AddListener(ShowNativeFullscreen);


        StartCoroutine(WaitForNetworkLoadedCoroutine());
    }

    private void Update()
    {
        if (Input.GetKeyDown(KeyCode.Space))
        {
            DLogger.LogInfo("Space key pressed");
            GameData.I.Save();
        }

        if (Input.GetKeyDown(KeyCode.Escape))
        {
            DLogger.LogInfo("Escape key pressed");
            GameData.I.Load();
        }
    }

    private IEnumerator WaitForNetworkLoadedCoroutine()
    {
        DLogger.LogInfo("WaitForNetworkLoadedCoroutine");
        yield return new WaitUntil(WaitForRemoteConfig);
        yield return new WaitForSeconds(1f);
        BannerControl.Instance.Show(bannerID: BannerID.default_banner);
        yield return new WaitUntil(() => AppOpenControl.I.IsLoaded(AppOpenID.app_open));
        AppOpenControl.I.Show(AppOpenID.app_open);
    }

    private bool WaitForRemoteConfig()
    {
        return RemoteConfigInstance.IsFetchSuccess;
    }

    private void WaitForNetworkLoaded()
    {
        var setting = MediationConfigSDK.Instance;
        _currentNetwork = setting.Adapters[0];
        networkDropdown.value = (int)_currentNetwork;
    }

    private void OnNetworkChanged(int arg0)
    {
        _currentNetwork = (MediationType)arg0;
    }

    private void OnDisable()
    {
        networkDropdown.onValueChanged.RemoveListener(OnNetworkChanged);
        bannerButton.onClick.RemoveListener(ShowBanner);
        bannerTopButton.onClick.RemoveListener(ShowBannerTop);
        bannerHideButton.onClick.RemoveListener(HideBanner);
        bannerTopHideButton.onClick.RemoveListener(HideBannerTop);
        interstitialButton.onClick.RemoveListener(ShowInterstitial);
        rewardedVideoButton.onClick.RemoveListener(ShowRewardedVideo);
        nativeBannerButton.onClick.RemoveListener(Extend);
        nativeBannerHideButton.onClick.RemoveListener(Collase);
        nativeFullscreenButton.onClick.RemoveListener(ShowNativeFullscreen);
    }

    public void ShowBanner()
    {
        BannerControl.Instance.Show(bannerID: BannerID.default_banner);
        text.text = "BannerBottom";
    }

    public void ShowBannerTop()
    {
        // BannerControl.Instance.Show(bannerID: EBannerID.default_in_game);
        text.text = "BannerTop";
    }

    public void HideBanner()
    {
        BannerControl.Instance.Hide(AdPositionSDK.Bottom);
        text.text = "BannerHide";
    }

    public void Extend()
    {
        BannerControl.Instance.Show(bannerID: BannerID.default_banner, true);
        text.text = "Extend";
    }

    public void Collase()
    {
        BannerControl.Instance.Collapse(BannerID.default_banner);
        text.text = "Collase";
    }

    public void HideBannerTop()
    {
        BannerControl.Instance.Hide(AdPositionSDK.Top);
        text.text = "BannerTopHide";
    }

    public void ShowInterstitial()
    {
        InterstitialControl.Instance.Show(callback: (isSuccess) => { DLogger.LogInfo($"interstitial: {isSuccess}"); });
        text.text = "Interstitial";
    }

    public void ShowRewardedVideo()
    {
        RewardControl.Instance.Show(callback: (isSuccess) => { DLogger.LogInfo($"reward: {isSuccess}"); });
    }


    public void ShowNativeFullscreen()
    {
        NativeFullscreenControl.Instance.Show(
            callback: (isSuccess) => { DLogger.LogInfo($"nativefullscreen: {isSuccess}"); });
        text.text = "NativeFullscreen";
    }
}