using System;
using System.Collections;
using DSDK.Core;
using DSDK.Logger;
using DSDK.Remote;
using UnityEngine;

namespace DSDK.Mediation
{
    public abstract class FullscreenAdapterBase : AdTypeAdapterBase, IFullscreenAdapter
    {
        #region Properties

        protected int ShowIndex = 0;
        protected EWayToLoadAds WayToLoadAds = EWayToLoadAds.OnlyOne;
        protected virtual int MaxLoad => 3;
        protected readonly int MaxLoadAdAtTheSameTime = 3;
        protected readonly UnitStatus[] UnitStatusList;
        protected readonly bool PreloadAdsAfterClose = true;
        protected readonly bool PreloadAdsAfterShowFail = true;
        protected virtual int MaxRetryCount { get; set; }
        protected string AdPlacement = "";
        public double Revenue { get; protected set; } = 0;
        public double FloorValue { get; set; } = 0;
        public string FloorCurrencyCode { get; set; } = "USD";
        protected bool IsShowSuccess = false;
        protected virtual bool CloseWithReward => IsShowSuccess;
        public Action<bool> OnLoadCallback { get; protected set; }
        public Action<bool> OnShowCallback { get; protected set; }
        public Action<bool> OnCloseCallback { get; protected set; }
        public Action OnMaxRetryCallback { get; protected set; }

        #endregion

        #region Constructor

        protected FullscreenAdapterBase(AdTypesSetting settings, bool isTesting = false, int priority = 0) : base(settings, isTesting, priority)
        {
            UnitStatusList = new UnitStatus[Settings.Keys.Length];
            MaxLoadAdAtTheSameTime = Mathf.Min(MaxLoad, Settings.Keys.Length);
            for (int i = 0; i < MaxLoadAdAtTheSameTime; i++)
            {
                UnitStatusList[i] = new UnitStatus
                {
                    AdUnitId = isTesting
                        ? (string.IsNullOrEmpty(TestingID) ? Settings.Keys[i] : TestingID)
                        : Settings.Keys[i],
                    Revenue = 0,
                    IsLoaded = false,
                    IsShowed = false,
                    IsRetrying = false,
                    RetryCount = 0,
                    AdNetwork = ""
                };
                DLogger.LogDebug($"[DSDK] [{GetType().Name}] Constructor: {UnitStatusList[i].AdUnitId}");
            }
        }

        #endregion

        #region Methods

        public void SetCallback(Action<bool> onLoad = null, Action<bool> onShow = null, Action<bool> onClose = null, Action onMaxRetry = null)
        {
            OnLoadCallback = onLoad;
            OnShowCallback = onShow;
            OnCloseCallback = onClose;
            OnMaxRetryCallback = onMaxRetry;
        }

        public abstract void Show(Action<bool> onComplete = null, string placement = "", Action<bool> onRequestShow = null, float delay = 0);

        protected void ActionAfterMaxRetry(int index)
        {
            switch (WayToLoadAds)
            {
                case EWayToLoadAds.Waterfall:
                    Destroy();
                    if (ShowIndex < MaxLoadAdAtTheSameTime - 1)
                    {
                        ShowIndex++;
                        Create();
                        OnCallbackLoadFailure("Load next key", index);
                    }
                    else
                    {
                        ShowIndex = 0;
                    }

                    break;
                case EWayToLoadAds.OnlyOne:
                    ShowIndex = 0;
                    break;
                case EWayToLoadAds.AllAndCompare:
                    break;
            }
        }

        public virtual bool IsLoaded()
        {
            foreach (var unit in UnitStatusList)
            {
                // DLogger.Log($"[{GetType().Name}] IsLoaded: {unit.AdUnitId} {unit.IsLoaded}");
                if (unit.IsLoaded)
                {
                    return true;
                }
            }

            return false;
        }

        public virtual bool IsShowed()
        {
            foreach (var unit in UnitStatusList)
            {
                // DLogger.Log($"[{GetType().Name}] IsShowed: {unit.AdUnitId} {unit.IsShowed}");
                if (unit.IsShowed)
                {
                    return true;
                }
            }

            return false;
        }

        public virtual bool IsRetrying()
        {
            foreach (var unit in UnitStatusList)
            {
                if (unit.IsRetrying || !unit.IsLoaded)
                    return true;
            }

            return false;
        }

        protected IEnumerator RetryLoadWithBackoff(int index)
        {
            if (UnitStatusList[index].IsRetrying)
            {
                DLogger.LogDebug($"[{GetType().Name}] is retrying, skip", channel: "Mediation");
                yield break;
            }

            UnitStatusList[index].IsRetrying = true;
            // Exponential backoff: 2s, 4s, 8s
            yield return new WaitForSeconds(Mathf.Pow(2, UnitStatusList[index].RetryCount));
            UnitStatusList[index].IsRetrying = false;
            Load();
        }

        public virtual void StartCountdown()
        {
            DLogger.LogDebug($"[DSDK] [{GetType().Name}] StartCountdown", channel: "Mediation");
        }

        #endregion

        #region Skip

        protected virtual bool SkipCreate()
        {
#if UNITY_EDITOR
            EditorAdsManager.Instance.CreateFullscreenAd(EditorAdUnitId, NetworkType, Type);
            EditorAdsManager.Instance.SetCallback(EditorAdUnitId, NetworkType, Type, OnCallbackLoadSuccess, OnCallbackLoadFailure, OnCallbackShowSuccess, OnCallbackShowFailure, OnCallbackClosed, null, null);
            return true;
#else
            return false;
#endif
        }

        protected virtual bool SkipLoad()
        {
            if (IsRequestingLoad)
            {
                DLogger.LogDebug($"[{GetType().Name}] Ad request load duplicate, check your logic",
                    channel: "Mediation");
                return true;
            }

            for (int i = 0; i < UnitStatusList.Length; i++)
            {
                if (UnitStatusList[i].IsRetrying || UnitStatusList[i].IsLoaded)
                {
                    return true;
                }
            }

            switch (WayToLoadAds)
            {
                case EWayToLoadAds.OnlyOne:
                case EWayToLoadAds.Waterfall:
                case EWayToLoadAds.AllAndCached:
                    break;
                case EWayToLoadAds.AllAndCompare:
                    Destroy();
                    Create();
                    break;
            }

#if UNITY_EDITOR
            if (!EditorAdsManager.Instance.HasAd(EditorAdUnitId, NetworkType, Type))
            {
                Create();
            }

            OnCallbackLoadRequest();
            EditorAdsManager.Instance.LoadAd(EditorAdUnitId, NetworkType, Type);
            return true;
#else
            return false;
#endif
        }

        protected virtual bool SkipShow(Action<bool> onComplete = null, string placement = "", Action<bool> onRequestShow = null, float delay = 0)
        {
            DLogger.LogDebug($"[{GetType().Name}] SkipShow:" +
                             $" ShowIndex: {ShowIndex} " +
                             $" AdUnitId: {UnitStatusList[ShowIndex].AdUnitId} " +
                             $" IsLoaded: {UnitStatusList[ShowIndex].IsLoaded} " +
                             $" IsRequestingShow: {IsRequestingShow} " +
                             $" IsShowed: {IsShowed()}");
            if (IsRequestingShow || IsShowed())
            {
                onComplete?.Invoke(false);
                onRequestShow?.Invoke(false);
                return true;
            }

            if (IsRequestingLoad)
            {
                onComplete?.Invoke(false);
                onRequestShow?.Invoke(false);
                return true;
            }

            AdPlacement = placement;

#if !UNITY_EDITOR
            OnCloseCallback = onComplete;
#endif

#if UNITY_EDITOR
            if (UnitStatusList[0].IsLoaded)
            {
                OnCallbackShowRequest();
                EditorAdsManager.Instance.ShowAd(EditorAdUnitId, NetworkType, Type, onComplete);
                onRequestShow?.Invoke(true);
            }
            else
            {
                onComplete?.Invoke(false);
                onRequestShow?.Invoke(false);
                OnCallbackShowFailure("Ad is not ready");
            }

            return true;
#else
            return false;
#endif
        }

        protected virtual bool SkipDestroy()
        {
#if UNITY_EDITOR
            EditorAdsManager.Instance.UnsetCallback(EditorAdUnitId, NetworkType, Type, OnCallbackLoadSuccess,
                OnCallbackLoadFailure, OnCallbackShowSuccess, OnCallbackShowFailure, OnCallbackClosed, null, null);
            EditorAdsManager.Instance.DestroyAd(EditorAdUnitId, NetworkType, Type);
            return true;
#else
            return false;
#endif
        }

        #endregion

        #region Callback

        protected override void OnCallbackRequestSuccess(int index, string demandSource)
        {
            IsRequestingLoad = false;

            if (NetworkType == MediationType.costcenter_s2sbidding)
            {
                if (AdManager.Instance.IsEnableCostCenterS2sBidding)
                {
                    if (Type == AdTypes.interstitial)
                    {
                        InterstitialControl.Instance.DemandWinner = AdDemand.s2s;
                    }
                    else if (Type == AdTypes.rewarded)
                    {
                        RewardControl.Instance.DemandWinner = AdDemand.s2s;
                    }
                }
            }

            DLogger.LogInfo($"[{GetType().Name}] Callback request success: {UnitStatusList[index].AdUnitId}, Winner: {demandSource}", channel: "Mediation");
        }

        protected override void OnCallbackRequestFailed(string errorCode, int index)
        {
            IsRequestingLoad = false;

            if (NetworkType == MediationType.costcenter_s2sbidding)
            {
                if (AdManager.Instance.IsEnableCostCenterS2sBidding)
                {
                    if (Type == AdTypes.interstitial)
                    {
                        InterstitialControl.Instance.DemandWinner = AdDemand.mediation;
                    }
                    else if (Type == AdTypes.rewarded)
                    {
                        RewardControl.Instance.DemandWinner = AdDemand.mediation;
                    }
                }
            }

            DLogger.LogInfo($"[{GetType().Name}] Callback request failed: {UnitStatusList[index].AdUnitId}, Error: {errorCode}", channel: "Mediation");
        }

        protected override void OnCallbackLoadRequest()
        {
            for (int i = 0; i < UnitStatusList.Length; i++)
            {
                UnitStatusList[i].IsLoaded = false;
                UnitStatusList[i].IsShowed = false;
                UnitStatusList[i].AdNetwork = "admob";
            }

            Metrics.StartLoad();
            IsRequestingLoad = true;
            AdPlacement = "none";
            DLogger.LogDebug($"[{GetType().Name}] Ad load request - {UnitStatusList[ShowIndex].AdUnitId}", channel: "Mediation");
            TrackingManagerWrapper.Instance.TrackingAdEvent(mediation: NetworkType, adNetwork: string.Empty, adFormat: Type, state: AdState.RequestLoad, placement: AdPlacement);
        }

        protected override void OnCallbackLoadSuccess(int index)
        {
            Metrics.EndLoad(true);
            IsRequestingLoad = false;
            UnitStatusList[index].IsLoaded = true;
            UnitStatusList[index].IsRetrying = false;
            OnLoadCallback?.Invoke(true);
            DLogger.LogDebug($"[{GetType().Name}] Ad loaded successfully - {UnitStatusList[index].AdUnitId}", channel: "Mediation");
            TrackingManagerWrapper.Instance.TrackingAdEvent(mediation: NetworkType,
                adNetwork: UnitStatusList[index].AdNetwork, adFormat: Type, state: AdState.Loaded,
                placement: AdPlacement, loadTime: Metrics.LastLoadDuration, retry: UnitStatusList[index].RetryCount);
            UnitStatusList[index].RetryCount = 0;

            if (NetworkType != MediationType.costcenter_s2sbidding)
            {
                if (AdManager.Instance.IsEnableCostCenterS2sBidding)
                {
                    if (Type == AdTypes.interstitial)
                    {
                        InterstitialControl.Instance.DemandWinner = AdDemand.mediation;

                        InterstitialControl.Instance.SetFloorValue(DSDK.Mediation.InterstitialID.default_interstitial_costcenter, Revenue * 1000);
                        InterstitialControl.Instance.Load(DSDK.Mediation.InterstitialID.default_interstitial_costcenter);
                    }
                    else if (Type == AdTypes.rewarded)
                    {
                        RewardControl.Instance.DemandWinner = AdDemand.mediation;

                        RewardControl.Instance.SetFloorValue(DSDK.Mediation.RewardedID.default_reward_costcenter, Revenue * 1000);
                        RewardControl.Instance.Load(DSDK.Mediation.RewardedID.default_reward_costcenter);
                    }
                }
            }
            else
            {
                if (AdManager.Instance.IsEnableCostCenterS2sBidding)
                {
                    if (Type == AdTypes.interstitial)
                    {
                        InterstitialControl.Instance.DemandWinner = AdDemand.s2s;
                    }
                    else if (Type == AdTypes.rewarded)
                    {
                        RewardControl.Instance.DemandWinner = AdDemand.s2s;
                    }
                }
            }
        }

        protected override void OnCallbackLoadFailure(string errorCode, int index)
        {
            Metrics.EndLoad(false);
            UnitStatusList[index].IsLoaded = false;
            UnitStatusList[index].AdNetwork = "admob";
            IsRequestingLoad = false;

            DLogger.LogError($"[{GetType().Name}] Failed to load ad: {errorCode} - {UnitStatusList[index].AdUnitId}", channel: "Mediation");
            OnLoadCallback?.Invoke(false);

            // if banner, don't retry, cause it set on MO automatically
            if (Type == AdTypes.banner || Type == AdTypes.native_banner) return;
            UnitStatusList[index].RetryCount++;
            if (UnitStatusList[index].RetryCount < MaxRetryCount)
            {
                SDKCore.I.StartCoroutine(RetryLoadWithBackoff(index));
            }
            else
            {
                UnitStatusList[index].IsRetrying = false;
                UnitStatusList[index].RetryCount = 0;
                OnMaxRetryCallback?.Invoke();
                ActionAfterMaxRetry(index);
                DLogger.LogError($"[{GetType().Name}] Max retry count reached - {UnitStatusList[index].AdUnitId}", channel: "Mediation");
            }

            TrackingManagerWrapper.Instance.TrackingAdEvent(mediation: NetworkType,
                adNetwork: UnitStatusList[index].AdNetwork, adFormat: Type, state: AdState.LoadFailed,
                placement: AdPlacement, errorCode: errorCode, loadTime: Metrics.LastLoadDuration,
                retry: UnitStatusList[index].RetryCount);

            if (NetworkType != MediationType.costcenter_s2sbidding)
            {
                if (AdManager.Instance.IsEnableCostCenterS2sBidding)
                {
                    if (Type == AdTypes.interstitial)
                    {
                        InterstitialControl.Instance.SetFloorValue(DSDK.Mediation.InterstitialID.default_interstitial_costcenter, Revenue * 1000);
                        InterstitialControl.Instance.Load(DSDK.Mediation.InterstitialID.default_interstitial_costcenter);
                    }
                    else if (Type == AdTypes.rewarded)
                    {
                        RewardControl.Instance.SetFloorValue(DSDK.Mediation.RewardedID.default_reward_costcenter, Revenue * 1000);
                        RewardControl.Instance.Load(DSDK.Mediation.RewardedID.default_reward_costcenter);
                    }
                }
            }
            else
            {
                if (AdManager.Instance.IsEnableCostCenterS2sBidding)
                {
                    if (Type == AdTypes.interstitial)
                    {
                        InterstitialControl.Instance.DemandWinner = AdDemand.mediation;
                    }
                    else if (Type == AdTypes.rewarded)
                    {
                        RewardControl.Instance.DemandWinner = AdDemand.mediation;
                    }
                }
            }
        }

        protected override void OnCallbackShowRequest()
        {
            Metrics.StartShow();
            IsRequestingShow = true;
            DLogger.LogDebug($"[{GetType().Name}] Ad show request - {UnitStatusList[ShowIndex].AdUnitId}",
                channel: "Mediation");
            TrackingManagerWrapper.Instance.TrackingAdEvent(mediation: NetworkType,
                adNetwork: UnitStatusList[ShowIndex].AdNetwork, adFormat: Type, state: AdState.RequestShow,
                placement: AdPlacement);
        }

        protected override void OnCallbackShowing()
        {
            UnitStatusList[ShowIndex].IsShowed = true;

            DLogger.LogDebug($"[{GetType().Name}] Ad showing - {UnitStatusList[ShowIndex].AdUnitId}", channel: "Mediation");
        }

        protected override void OnCallbackShowSuccess()
        {
            Metrics.EndShow(true);
            IsRequestingShow = false;
            IsShowSuccess = true;
            UnitStatusList[ShowIndex].IsShowed = true;
            UnitStatusList[ShowIndex].IsLoaded = false;

            OnShowCallback?.Invoke(true);
            if (UnityUtils.TimeScale != 0)
            {
                UnityUtils.TimeScale = 0;
            }

            DLogger.LogDebug($"[{GetType().Name}] Ad showed successfully - {UnitStatusList[ShowIndex].AdUnitId}",
                channel: "Mediation");
            TrackingManagerWrapper.Instance.TrackingAdEvent(mediation: NetworkType,
                adNetwork: UnitStatusList[ShowIndex].AdNetwork, adFormat: Type, state: AdState.ShowSuccess,
                placement: AdPlacement, showTime: Metrics.LastShowDuration);
        }

        protected override void OnCallbackShowFailure(string errorCode)
        {
            Metrics.EndShow(false);
            IsRequestingShow = false;
            IsShowSuccess = false;
            UnitStatusList[ShowIndex].IsShowed = false;
            UnitStatusList[ShowIndex].IsLoaded = false;

            OnShowCallback?.Invoke(false);
            DLogger.LogError($"[{GetType().Name}] Failed to show ad: {errorCode} - {UnitStatusList[ShowIndex].AdUnitId}", channel: "Mediation");

            if (NetworkType != MediationType.costcenter_s2sbidding)
            {
                if (PreloadAdsAfterShowFail)
                {
                    SDKCore.I.StartCoroutine(RetryLoadWithBackoff(ShowIndex));
                }
            }
            else
            {
                if (AdManager.Instance.IsEnableCostCenterS2sBidding)
                {
                    if (Type == AdTypes.interstitial)
                    {
                        InterstitialControl.Instance.DemandWinner = AdDemand.mediation;
                    }
                    else if (Type == AdTypes.rewarded)
                    {
                        RewardControl.Instance.DemandWinner = AdDemand.mediation;
                    }
                }
            }

            TrackingManagerWrapper.Instance.TrackingAdEvent(mediation: NetworkType,
                adNetwork: UnitStatusList[ShowIndex].AdNetwork, adFormat: Type, state: AdState.ShowFailed,
                placement: AdPlacement, errorCode: errorCode, showTime: Metrics.LastShowDuration);
        }

        protected override void OnCallbackClicked()
        {
            DLogger.LogDebug($"[{GetType().Name}] Ad clicked - {UnitStatusList[ShowIndex].AdUnitId}", channel: "Mediation");
            TrackingManagerWrapper.Instance.TrackingAdEvent(mediation: NetworkType,
                adNetwork: UnitStatusList[ShowIndex].AdNetwork, adFormat: Type, state: AdState.Click,
                placement: AdPlacement);
        }

        protected override void OnCallbackClosed()
        {
            IsRequestingShow = false;
            UnitStatusList[ShowIndex].IsShowed = false;
            UnitStatusList[ShowIndex].IsLoaded = false;
            if (UnityUtils.TimeScale == 0)
            {
                UnityUtils.TimeScale = UnityUtils.LastTimeScale;
            }

            OnCloseCallback?.Invoke(CloseWithReward);
            OnCloseCallback = null;
            DLogger.LogDebug($"[{GetType().Name}] Ad closed - {UnitStatusList[ShowIndex].AdUnitId}", channel: "Mediation");

            if (NetworkType == MediationType.costcenter_s2sbidding)
            {
                if (AdManager.Instance.IsEnableCostCenterS2sBidding)
                {
                    if (Type == AdTypes.interstitial)
                    {
                        InterstitialControl.Instance.DemandWinner = AdDemand.mediation;
                    }
                    else if (Type == AdTypes.rewarded)
                    {
                        RewardControl.Instance.DemandWinner = AdDemand.mediation;
                    }
                }
            }

            if (NetworkType != MediationType.costcenter_s2sbidding)
            {
                if (PreloadAdsAfterClose)
                {
                    DLogger.LogDebug($"[{GetType().Name}] Starting preload after close", channel: "Mediation");
                    SDKCore.I.StartCoroutine(RetryLoadWithBackoff(ShowIndex));
                }
            }

            TrackingManagerWrapper.Instance.TrackingAdEvent(mediation: NetworkType,
                adNetwork: UnitStatusList[ShowIndex].AdNetwork, adFormat: Type, state: AdState.Close,
                placement: AdPlacement);
            Revenue = 0;
        }

        protected override void OnCallbackDestroy()
        {
            IsRequestingShow = false;
            IsRequestingLoad = false;
            for (int i = 0; i < UnitStatusList.Length; i++)
            {
                UnitStatusList[i].IsLoaded = false;
                UnitStatusList[i].IsShowed = false;
            }

            if (NetworkType == MediationType.costcenter_s2sbidding)
            {
                if (AdManager.Instance.IsEnableCostCenterS2sBidding)
                {
                    if (Type == AdTypes.interstitial)
                    {
                        InterstitialControl.Instance.DemandWinner = AdDemand.mediation;
                    }
                    else if (Type == AdTypes.rewarded)
                    {
                        RewardControl.Instance.DemandWinner = AdDemand.mediation;
                    }
                }
            }

            DLogger.LogDebug($"[{GetType().Name}] Ad destroyed - {UnitStatusList[ShowIndex].AdUnitId}", channel: "Mediation");
        }

        // Method to get metrics report

        #endregion
    }
}