using UnityEngine;
using UnityEditor;
using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using DSDK.Logger;

namespace DSDK.Remote.Editor
{
    [InitializeOnLoad]
    public class RemoteConfigMonitorWindow : EditorWindow
    {
        private Vector2 _scrollPosition;
        private RemoteConfigMonitor _remoteConfigMonitor;
        private SerializedObject _serializedObject;
        private UnityEditor.Editor _editor;
        private GameObject _prefab;
        private List<System.Type> _dataTypes = new List<System.Type>();
        private bool _stylesInitialized;

        // UI Styles
        private GUIStyle _headerStyle;
        private GUIStyle _sectionHeaderStyle;
        private GUIStyle _buttonStyle;
        private GUIStyle _boxStyle;
        private GUIStyle _titleStyle;
        private Color _headerColor = new Color(0.2f, 0.4f, 0.7f);
        private Color _buttonColor = new Color(0.3f, 0.5f, 0.8f);
        private Texture2D _logoTexture;

        private const string PREFAB_PATH = "RemoteConfigMonitor";
        private const string SCRIPT_TEMPLATE = @"using DSDK.Remote;

namespace {0}
{{
    [System.Serializable]
    public class {1} : RemoteConfig<{1}>
    {{
        // TODO: Add your data fields here
    }}
}}";

        [MenuItem("Dmobin/Remote Config/Monitor")]
        public static void ShowWindow()
        {
            var window = GetWindow<RemoteConfigMonitorWindow>("Remote Config Monitor");
            window.Show();
        }

        private void OnEnable()
        {
            titleContent = new GUIContent("Remote Config Monitor");
            _stylesInitialized = false;

            EditorApplication.playModeStateChanged += OnPlayModeStateChanged;
            AssemblyReloadEvents.afterAssemblyReload += OnAfterAssemblyReload;

            FindDataSetting();
        }

        private void OnDisable()
        {
            AssemblyReloadEvents.afterAssemblyReload -= OnAfterAssemblyReload;
            EditorApplication.playModeStateChanged -= OnPlayModeStateChanged;
        }

        private void OnPlayModeStateChanged(PlayModeStateChange state)
        {
            FindDataSetting();
            Repaint();
        }

        private void OnAfterAssemblyReload()
        {
            FindDataSetting();
            Repaint();
            // CheckForNewDataScripts();
        }

        private void InitializeStyles()
        {
            _headerStyle = new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 14,
                alignment = TextAnchor.MiddleLeft,
                margin = new RectOffset(5, 5, 5, 5)
            };

            _sectionHeaderStyle = new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 12,
                alignment = TextAnchor.MiddleLeft,
                margin = new RectOffset(5, 5, 8, 5)
            };

            _buttonStyle = new GUIStyle(GUI.skin.button)
            {
                fontSize = 12,
                fontStyle = FontStyle.Bold,
                alignment = TextAnchor.MiddleCenter,
                margin = new RectOffset(5, 5, 3, 3),
                padding = new RectOffset(8, 8, 4, 4)
            };

            _boxStyle = new GUIStyle(EditorStyles.helpBox)
            {
                margin = new RectOffset(5, 5, 5, 5),
                padding = new RectOffset(10, 10, 10, 10)
            };

            _titleStyle = new GUIStyle(EditorStyles.largeLabel)
            {
                fontSize = 16,
                fontStyle = FontStyle.Bold,
                alignment = TextAnchor.MiddleCenter,
                margin = new RectOffset(0, 0, 10, 15)
            };

            _stylesInitialized = true;
        }

        private void OnGUI()
        {
            // Đảm bảo styles đã được khởi tạo
            if (!_stylesInitialized)
            {
                if (EditorStyles.boldLabel != null)
                {
                    InitializeStyles();
                }
                else
                {
                    EditorGUILayout.LabelField("Đang khởi tạo...");
                    return;
                }
            }

            // Draw Banner with Logo
            DrawBanner();

            if (_remoteConfigMonitor == null || _serializedObject == null)
            {
                DrawNoMonitorState();
                return;
            }

            // Main content
            using (new EditorGUILayout.VerticalScope())
            {
                DrawHeader();

                EditorGUILayout.Space(10);
                using (new EditorGUILayout.VerticalScope(_boxStyle))
                {
                    EditorGUILayout.LabelField("Cấu hình Remote Config", _sectionHeaderStyle);
                    EditorGUILayout.Space(5);

                    using (var scrollView = new EditorGUILayout.ScrollViewScope(_scrollPosition))
                    {
                        _scrollPosition = scrollView.scrollPosition;
                        DrawRemoteConfigSettingInfo();
                    }
                }

                EditorGUILayout.Space(10);
                DrawFooter();
            }

            // Auto repaint để cập nhật realtime
            if (Application.isPlaying)
            {
                Repaint();
            }
        }

        private void DrawBanner()
        {
            Rect bannerRect = EditorGUILayout.GetControlRect(false, 40);
            EditorGUI.DrawRect(bannerRect, _headerColor);

            GUI.color = Color.white;
            EditorGUI.LabelField(bannerRect, "REMOTE CONFIG MONITOR", _titleStyle);
            GUI.color = Color.white;

            EditorGUILayout.Space(5);
        }

        private void DrawNoMonitorState()
        {
            using (new EditorGUILayout.VerticalScope(_boxStyle))
            {
                EditorGUILayout.HelpBox($"Không tìm thấy RemoteConfigMonitor Prefab tại {PREFAB_PATH}", MessageType.Warning);

                EditorGUILayout.Space(10);

                using (new EditorGUILayout.HorizontalScope())
                {
                    GUILayout.FlexibleSpace();
                    if (GUILayout.Button("Mở thư mục Resources", _buttonStyle, GUILayout.Width(200)))
                    {
                        EditorGUIUtility.PingObject(_remoteConfigMonitor);
                    }
                    GUILayout.FlexibleSpace();
                }
            }
        }

        private void DrawHeader()
        {
            using (new EditorGUILayout.VerticalScope(EditorStyles.helpBox))
            {
                string playModeInfo = Application.isPlaying
                    ? "Chế độ: Play - Sử dụng Instance trong Scene"
                    : "Chế độ: Edit - Sử dụng Prefab từ Resources";

                EditorGUILayout.LabelField(playModeInfo, EditorStyles.centeredGreyMiniLabel);

                using (new EditorGUILayout.HorizontalScope())
                {
                    GUI.backgroundColor = _buttonColor;
                    if (GUILayout.Button("Thêm Remote Config", _buttonStyle))
                    {
                        CreateNewRemoteConfigScript();
                    }

                    if (GUILayout.Button("Tạo Instance", _buttonStyle))
                    {
                        GenerateMonitorInstances();
                    }

                    GUI.backgroundColor = Color.red;
                    if (GUILayout.Button("Xoá hết Instance", _buttonStyle))
                    {
                        ClearAllInstances();
                    }

                    GUI.backgroundColor = _buttonColor;
                    if (GUILayout.Button("Ping", _buttonStyle, GUILayout.Width(80)))
                    {
                        if (Application.isPlaying)
                            EditorGUIUtility.PingObject(_remoteConfigMonitor.gameObject);
                        else
                            EditorGUIUtility.PingObject(_prefab);
                    }
                    GUI.backgroundColor = Color.white;
                }
            }
        }

        private void GenerateMonitorInstances()
        {
            try
            {
                EditorUtility.DisplayProgressBar("Đang xử lý", "Đang tìm kiếm các Remote Config...", 0.1f);

                // Find the RemoteConfigMonitor.cs file
                string[] guids = AssetDatabase.FindAssets("RemoteConfigMonitor t:Script");
                string remoteConfigMonitorPath = "";

                foreach (string guid in guids)
                {
                    string path = AssetDatabase.GUIDToAssetPath(guid);
                    if (path.EndsWith("RemoteConfigMonitor.cs"))
                    {
                        remoteConfigMonitorPath = path;
                        break;
                    }
                }

                if (string.IsNullOrEmpty(remoteConfigMonitorPath))
                {
                    EditorUtility.ClearProgressBar();
                    EditorUtility.DisplayDialog("Lỗi", "Không tìm thấy file RemoteConfigMonitor.cs", "OK");
                    return;
                }

                EditorUtility.DisplayProgressBar("Đang xử lý", "Đang đọc file RemoteConfigMonitor.cs...", 0.2f);
                // Read the RemoteConfigMonitor.cs file
                string scriptContent = System.IO.File.ReadAllText(remoteConfigMonitorPath);

                var dataTypes = new List<System.Type>();
                EditorUtility.DisplayProgressBar("Đang xử lý", "Đang tìm kiếm các lớp RemoteConfig...", 0.3f);

                // Xử lý an toàn khi tìm kiếm các lớp
                FindRemoteConfigClasses(dataTypes);

                if (dataTypes.Count == 0)
                {
                    EditorUtility.ClearProgressBar();
                    EditorUtility.DisplayDialog("Cảnh báo", "Không tìm thấy lớp RemoteConfig nào. Hãy kiểm tra lại các script của bạn.", "OK");
                    return;
                }

                // Log the found data types
                DLogger.LogDebug($"Found {dataTypes.Count} remote config types to add to RemoteConfigMonitor:");
                foreach (var type in dataTypes)
                {
                    DLogger.LogDebug($"- {type.FullName}");
                }

                EditorUtility.DisplayProgressBar("Đang xử lý", "Đang cập nhật file RemoteConfigMonitor.cs...", 0.7f);

                try
                {
                    // replace #region Monitor Fields
                    UpdateMonitorFields(scriptContent, dataTypes, out scriptContent);

                    // replace #region Get Default
                    UpdateGetDefault(scriptContent, dataTypes, out scriptContent);

                    // replace #region Get Instance
                    UpdateGetInstance(scriptContent, dataTypes, out scriptContent);

                    // replace #region Refresh Instance
                    UpdateRefreshInstance(scriptContent, dataTypes, out scriptContent);

                    EditorUtility.DisplayProgressBar("Đang xử lý", "Đang lưu file...", 0.9f);
                    // Write the updated content back to the file
                    System.IO.File.WriteAllText(remoteConfigMonitorPath, scriptContent);
                }
                catch (Exception ex)
                {
                    EditorUtility.ClearProgressBar();
                    DLogger.LogError($"Lỗi khi cập nhật file: {ex.Message}");
                    EditorUtility.DisplayDialog("Lỗi", $"Không thể cập nhật file RemoteConfigMonitor.cs: {ex.Message}", "OK");
                    return;
                }

                AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);

                EditorUtility.ClearProgressBar();
                EditorUtility.DisplayDialog("Thành công", $"RemoteConfigMonitor.cs đã được cập nhật với {dataTypes.Count} remote config types", "OK");
            }
            catch (Exception ex)
            {
                EditorUtility.ClearProgressBar();
                DLogger.LogError($"Error generating instance: {ex.Message}");
                EditorUtility.DisplayDialog("Lỗi", $"Không thể tạo instance: {ex.Message}", "OK");
            }
        }

        private void ClearAllInstances()
        {
            if (!EditorUtility.DisplayDialog("Xác nhận",
                "Bạn có chắc chắn muốn xóa tất cả các instance đã được generate?\n\nHành động này không thể hoàn tác!",
                "Xóa", "Hủy"))
            {
                return;
            }

            try
            {
                EditorUtility.DisplayProgressBar("Đang xử lý", "Đang tìm file RemoteConfigMonitor.cs...", 0.1f);

                // Find the RemoteConfigMonitor.cs file
                string[] guids = AssetDatabase.FindAssets("RemoteConfigMonitor t:Script");
                string remoteConfigMonitorPath = "";

                foreach (string guid in guids)
                {
                    string path = AssetDatabase.GUIDToAssetPath(guid);
                    if (path.EndsWith("RemoteConfigMonitor.cs"))
                    {
                        remoteConfigMonitorPath = path;
                        break;
                    }
                }

                if (string.IsNullOrEmpty(remoteConfigMonitorPath))
                {
                    EditorUtility.ClearProgressBar();
                    EditorUtility.DisplayDialog("Lỗi", "Không tìm thấy file RemoteConfigMonitor.cs", "OK");
                    return;
                }

                EditorUtility.DisplayProgressBar("Đang xử lý", "Đang đọc file RemoteConfigMonitor.cs...", 0.3f);
                string scriptContent = System.IO.File.ReadAllText(remoteConfigMonitorPath);

                EditorUtility.DisplayProgressBar("Đang xử lý", "Đang xóa các region...", 0.5f);

                // Clear all regions
                ClearMonitorFields(scriptContent, out scriptContent);
                ClearGetDefault(scriptContent, out scriptContent);
                ClearGetInstance(scriptContent, out scriptContent);
                ClearRefreshInstance(scriptContent, out scriptContent);

                EditorUtility.DisplayProgressBar("Đang xử lý", "Đang lưu file...", 0.9f);
                System.IO.File.WriteAllText(remoteConfigMonitorPath, scriptContent);

                AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);

                EditorUtility.ClearProgressBar();
                EditorUtility.DisplayDialog("Thành công", "Đã xóa tất cả các instance trong RemoteConfigMonitor.cs", "OK");
            }
            catch (Exception ex)
            {
                EditorUtility.ClearProgressBar();
                DLogger.LogError($"Lỗi khi xóa instances: {ex.Message}");
                EditorUtility.DisplayDialog("Lỗi", $"Không thể xóa instances: {ex.Message}", "OK");
            }
        }

        private void CreateNewRemoteConfigScript()
        {
            string path = EditorUtility.SaveFilePanel(
                "Tạo Remote Config Script Mới",
                "Assets/Dmobin/Monitor/RemoteConfig/Scripts/Monitors",
                "NewRemoteConfig.cs",
                "cs");

            if (string.IsNullOrEmpty(path)) return;

            // Convert to relative path
            path = path.Replace(Application.dataPath, "Assets");

            // Get namespace and class name
            string className = Path.GetFileNameWithoutExtension(path);
            string namespaceName = "DSDK.Remote";

            // Generate script content
            string scriptContent = string.Format(SCRIPT_TEMPLATE, namespaceName, className);

            // Create the script file
            File.WriteAllText(path, scriptContent);

            AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);

            // Select the new script
            var asset = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(path);
            Selection.activeObject = asset;
            EditorGUIUtility.PingObject(asset);
        }

        private void FindDataSetting()
        {
            try
            {
                // Cleanup old references
                if (_editor != null) DestroyImmediate(_editor);
                _editor = null;
                _serializedObject = null;
                _remoteConfigMonitor = null;
                _prefab = null;

                // Nếu đang play thì lấy từ scene
                if (Application.isPlaying)
                {
                    if (RemoteConfigMonitor.Instance != null)
                    {
                        _remoteConfigMonitor = RemoteConfigMonitor.Instance;
                    }
                    else
                    {
                        _prefab = Resources.Load<GameObject>(PREFAB_PATH);
                        if (_prefab != null)
                        {
                            _remoteConfigMonitor = _prefab.GetComponent<RemoteConfigMonitor>();
                        }
                    }
                }
                // Không thì lấy từ prefab
                else
                {
                    _prefab = Resources.Load<GameObject>(PREFAB_PATH);
                    if (_prefab != null)
                    {
                        _remoteConfigMonitor = _prefab.GetComponent<RemoteConfigMonitor>();
                    }
                }

                if (_remoteConfigMonitor != null)
                {
                    _serializedObject = new SerializedObject(_remoteConfigMonitor);
                    _editor = UnityEditor.Editor.CreateEditor(_remoteConfigMonitor);
                    _dataTypes = RemoteConfigBase.GetAllConfigTypes().ToList();
                }
            }
            catch (System.Exception e)
            {
                DLogger.LogError($"[RemoteConfigMonitor] Lỗi khi tìm RemoteConfigMonitor: {e}");
            }
        }

        private void DrawRemoteConfigSettingInfo()
        {
            using (new EditorGUILayout.VerticalScope(_boxStyle))
            {
                // Draw inspector
                EditorGUI.BeginChangeCheck();
                _serializedObject.Update();

                _editor.OnInspectorGUI();

                if (EditorGUI.EndChangeCheck())
                {
                    _serializedObject.ApplyModifiedProperties();

                    if (!Application.isPlaying)
                    {
                        // Đánh dấu prefab là dirty
                        EditorUtility.SetDirty(_remoteConfigMonitor);
                        if (_prefab != null)
                        {
                            PrefabUtility.RecordPrefabInstancePropertyModifications(_remoteConfigMonitor);
                            AssetDatabase.SaveAssets();
                        }
                    }
                    else
                    {
                        _remoteConfigMonitor = RemoteConfigMonitor.Instance;
                    }
                }
            }
        }

        private void DrawFooter()
        {
            // Phần footer đã được loại bỏ hoàn toàn
            // Có thể thêm các chức năng khác vào đây trong tương lai nếu cần
        }

        private void OnDestroy()
        {
            if (_editor != null)
            {
                DestroyImmediate(_editor);
                _editor = null;
            }
        }

        // Danh sách các class cần loại trừ
        private static readonly HashSet<string> ExcludedClassNames = new HashSet<string>
        {
            "AnalyticsConfigSDK",
            "FirebaseConfig",
            "AppOpenConfigSDK",
            "BannerConfigSDK",
            "InterstitialConfigSDK",
            "LoadAdsConfigSDK",
            "LoadingAdConfigSDK",
            "NativeFullScreenConfigSDK",
            "NativeImmersiveConfigSDK",
            "RewardedVideoConfigSDK",
            "MediationConfigSDK",
            "IAPConfigSDK",
            "StoreConfig"
        };

        // Phương thức để tìm kiếm các lớp RemoteConfig an toàn
        private void FindRemoteConfigClasses(List<System.Type> dataTypes)
        {

            var assemblies = System.AppDomain.CurrentDomain.GetAssemblies();

            foreach (var assembly in assemblies)
            {
                try
                {
                    var types = assembly.GetTypes()
                        .Where(t =>
                        {
                            try
                            {
                                return t.IsClass && !t.IsAbstract &&
                                    t.Namespace != null &&
                                    (t.Namespace.StartsWith("DSDK.Remote") ||
                                    t.Namespace.StartsWith("DSDK.Analytics") ||
                                    t.Namespace.StartsWith("DSDK.InAppPurchase") ||
                                    t.Namespace.StartsWith("DSDK.Mediation"));
                            }
                            catch
                            {
                                return false;
                            }
                        })
                        .Where(t =>
                        {
                            try
                            {
                                var baseType = t.BaseType;
                                // Check if type inherits from any RemoteConfig<> generic class
                                while (baseType != null)
                                {
                                    if (baseType.IsGenericType &&
                                        baseType.GetGenericTypeDefinition().Name.StartsWith("RemoteConfig`"))
                                    {
                                        return true;
                                    }
                                    baseType = baseType.BaseType;
                                }
                                return false;
                            }
                            catch
                            {
                                return false;
                            }
                        })
                        .Where(t => !ExcludedClassNames.Contains(t.Name)); // Loại trừ các class không mong muốn

                    dataTypes.AddRange(types);
                }
                catch (Exception)
                {
                    // Skip problematic assemblies
                    continue;
                }
            }

            try
            {
                // Add any additional types from RemoteConfigBase if that method exists and finds different types
                var dbTypes = RemoteConfigBase.GetAllConfigTypes();
                if (dbTypes != null)
                {
                    foreach (var type in dbTypes)
                    {
                        if (!dataTypes.Contains(type) && !ExcludedClassNames.Contains(type.Name))
                        {
                            dataTypes.Add(type);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                DLogger.LogWarning($"Không thể lấy thông tin từ RemoteConfigBase.GetAllConfigTypes(): {ex.Message}");
            }
        }

        // Cập nhật phần Monitor Fields
        private void UpdateMonitorFields(string scriptContent, List<System.Type> dataTypes, out string updatedContent)
        {
            try
            {
                var monitorFields = scriptContent.IndexOf("#region Monitor Fields");
                if (monitorFields < 0)
                {
                    DLogger.LogError("Không tìm thấy #region Monitor Fields trong RemoteConfigMonitor.cs");
                    updatedContent = scriptContent;
                    return;
                }

                var endMonitorFields = scriptContent.IndexOf("#endregion", monitorFields);
                if (endMonitorFields < 0)
                {
                    DLogger.LogError("Không tìm thấy #endregion cho Monitor Fields trong RemoteConfigMonitor.cs");
                    updatedContent = scriptContent;
                    return;
                }

                var bodyMonitorFields = scriptContent.Substring(monitorFields, endMonitorFields - monitorFields);
                var newBodyMonitorFields = "#region Monitor Fields";
                foreach (var type in dataTypes)
                {
                    newBodyMonitorFields += $"\n\t[SerializeField] private {type.Name} _{char.ToLowerInvariant(type.Name[0]) + type.Name.Substring(1)};";
                }
                newBodyMonitorFields += "\n\t";
                updatedContent = scriptContent.Replace(bodyMonitorFields, newBodyMonitorFields);
            }
            catch (Exception ex)
            {
                DLogger.LogError($"Lỗi khi cập nhật Monitor Fields: {ex.Message}");
                updatedContent = scriptContent;
            }
        }

        // Cập nhật phần Get Default
        private void UpdateGetDefault(string scriptContent, List<System.Type> dataTypes, out string updatedContent)
        {
            try
            {
                var getInstanceMethod = scriptContent.IndexOf("#region Get Default");
                if (getInstanceMethod < 0)
                {
                    DLogger.LogError("Không tìm thấy #region Get Default trong RemoteConfigMonitor.cs");
                    updatedContent = scriptContent;
                    return;
                }

                var endGetInstanceMethod = scriptContent.IndexOf("#endregion", getInstanceMethod);
                if (endGetInstanceMethod < 0)
                {
                    DLogger.LogError("Không tìm thấy #endregion cho Get Default trong RemoteConfigMonitor.cs");
                    updatedContent = scriptContent;
                    return;
                }

                var methodBody = scriptContent.Substring(getInstanceMethod, endGetInstanceMethod - getInstanceMethod);
                var newMethodBody = "#region Get Default";
                foreach (var type in dataTypes)
                {
                    string varName = $"_{char.ToLowerInvariant(type.Name[0]) + type.Name.Substring(1)}";
                    newMethodBody += $"\n\t\t{type.Name}.SetDefaultValue({varName});";
                }
                newMethodBody += "\n\t\t";
                updatedContent = scriptContent.Replace(methodBody, newMethodBody);
            }
            catch (Exception ex)
            {
                DLogger.LogError($"Lỗi khi cập nhật Get Default: {ex.Message}");
                updatedContent = scriptContent;
            }
        }

        // Cập nhật phần Get Instance
        private void UpdateGetInstance(string scriptContent, List<System.Type> dataTypes, out string updatedContent)
        {
            try
            {
                var getInstanceMethod = scriptContent.IndexOf("#region Get Instance");
                if (getInstanceMethod < 0)
                {
                    DLogger.LogError("Không tìm thấy #region Get Instance trong RemoteConfigMonitor.cs");
                    updatedContent = scriptContent;
                    return;
                }

                var endGetInstanceMethod = scriptContent.IndexOf("#endregion", getInstanceMethod);
                if (endGetInstanceMethod < 0)
                {
                    DLogger.LogError("Không tìm thấy #endregion cho Get Instance trong RemoteConfigMonitor.cs");
                    updatedContent = scriptContent;
                    return;
                }

                var methodBody = scriptContent.Substring(getInstanceMethod, endGetInstanceMethod - getInstanceMethod);
                var newMethodBody = "#region Get Instance";
                foreach (var type in dataTypes)
                {
                    newMethodBody += $"\n\t\t{type.Name}.Instance.ForceFetch();";
                }
                newMethodBody += "\n\t\t";
                updatedContent = scriptContent.Replace(methodBody, newMethodBody);
            }
            catch (Exception ex)
            {
                DLogger.LogError($"Lỗi khi cập nhật Get Instance: {ex.Message}");
                updatedContent = scriptContent;
            }
        }

        // Cập nhật phần Refresh Instance
        private void UpdateRefreshInstance(string scriptContent, List<System.Type> dataTypes, out string updatedContent)
        {
            try
            {
                var refreshInstanceMethod = scriptContent.IndexOf("#region Refresh Instance");
                if (refreshInstanceMethod < 0)
                {
                    DLogger.LogError("Không tìm thấy #region Refresh Instance trong RemoteConfigMonitor.cs");
                    updatedContent = scriptContent;
                    return;
                }

                var endRefreshInstanceMethod = scriptContent.IndexOf("#endregion", refreshInstanceMethod);
                if (endRefreshInstanceMethod < 0)
                {
                    DLogger.LogError("Không tìm thấy #endregion cho Refresh Instance trong RemoteConfigMonitor.cs");
                    updatedContent = scriptContent;
                    return;
                }

                var methodBody = scriptContent.Substring(refreshInstanceMethod, endRefreshInstanceMethod - refreshInstanceMethod);
                var newMethodBody = "#region Refresh Instance";
                foreach (var type in dataTypes)
                {
                    string varName = $"_{char.ToLowerInvariant(type.Name[0]) + type.Name.Substring(1)}";
                    newMethodBody += $"\n\t\t{varName} = {type.Name}.Instance;";
                }
                newMethodBody += "\n\t\t";
                updatedContent = scriptContent.Replace(methodBody, newMethodBody);
            }
            catch (Exception ex)
            {
                DLogger.LogError($"Lỗi khi cập nhật Refresh Instance: {ex.Message}");
                updatedContent = scriptContent;
            }
        }

        // Các hàm Clear để xóa nội dung các region
        private void ClearMonitorFields(string scriptContent, out string updatedContent)
        {
            try
            {
                var monitorFields = scriptContent.IndexOf("#region Monitor Fields");
                if (monitorFields < 0)
                {
                    updatedContent = scriptContent;
                    return;
                }

                var endMonitorFields = scriptContent.IndexOf("#endregion", monitorFields);
                if (endMonitorFields < 0)
                {
                    updatedContent = scriptContent;
                    return;
                }

                var bodyMonitorFields = scriptContent.Substring(monitorFields, endMonitorFields - monitorFields);
                var newBodyMonitorFields = "#region Monitor Fields\n\t";
                updatedContent = scriptContent.Replace(bodyMonitorFields, newBodyMonitorFields);
            }
            catch (Exception ex)
            {
                DLogger.LogError($"Lỗi khi xóa Monitor Fields: {ex.Message}");
                updatedContent = scriptContent;
            }
        }

        private void ClearGetDefault(string scriptContent, out string updatedContent)
        {
            try
            {
                var getDefaultMethod = scriptContent.IndexOf("#region Get Default");
                if (getDefaultMethod < 0)
                {
                    updatedContent = scriptContent;
                    return;
                }

                var endGetDefaultMethod = scriptContent.IndexOf("#endregion", getDefaultMethod);
                if (endGetDefaultMethod < 0)
                {
                    updatedContent = scriptContent;
                    return;
                }

                var methodBody = scriptContent.Substring(getDefaultMethod, endGetDefaultMethod - getDefaultMethod);
                var newMethodBody = "#region Get Default\n\t\t";
                updatedContent = scriptContent.Replace(methodBody, newMethodBody);
            }
            catch (Exception ex)
            {
                DLogger.LogError($"Lỗi khi xóa Get Default: {ex.Message}");
                updatedContent = scriptContent;
            }
        }

        private void ClearGetInstance(string scriptContent, out string updatedContent)
        {
            try
            {
                var getInstanceMethod = scriptContent.IndexOf("#region Get Instance");
                if (getInstanceMethod < 0)
                {
                    updatedContent = scriptContent;
                    return;
                }

                var endGetInstanceMethod = scriptContent.IndexOf("#endregion", getInstanceMethod);
                if (endGetInstanceMethod < 0)
                {
                    updatedContent = scriptContent;
                    return;
                }

                var methodBody = scriptContent.Substring(getInstanceMethod, endGetInstanceMethod - getInstanceMethod);
                var newMethodBody = "#region Get Instance\n\t\t";
                updatedContent = scriptContent.Replace(methodBody, newMethodBody);
            }
            catch (Exception ex)
            {
                DLogger.LogError($"Lỗi khi xóa Get Instance: {ex.Message}");
                updatedContent = scriptContent;
            }
        }

        private void ClearRefreshInstance(string scriptContent, out string updatedContent)
        {
            try
            {
                var refreshInstanceMethod = scriptContent.IndexOf("#region Refresh Instance");
                if (refreshInstanceMethod < 0)
                {
                    updatedContent = scriptContent;
                    return;
                }

                var endRefreshInstanceMethod = scriptContent.IndexOf("#endregion", refreshInstanceMethod);
                if (endRefreshInstanceMethod < 0)
                {
                    updatedContent = scriptContent;
                    return;
                }

                var methodBody = scriptContent.Substring(refreshInstanceMethod, endRefreshInstanceMethod - refreshInstanceMethod);
                var newMethodBody = "#region Refresh Instance\n\t\t";
                updatedContent = scriptContent.Replace(methodBody, newMethodBody);
            }
            catch (Exception ex)
            {
                DLogger.LogError($"Lỗi khi xóa Refresh Instance: {ex.Message}");
                updatedContent = scriptContent;
            }
        }
    }
}