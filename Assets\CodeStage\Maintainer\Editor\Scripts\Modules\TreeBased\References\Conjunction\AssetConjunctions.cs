﻿#region copyright
// -------------------------------------------------------
// Copyright (C) <PERSON><PERSON><PERSON><PERSON> [https://codestage.net]
// -------------------------------------------------------
#endregion

namespace CodeStage.Maintainer.References
{
	using System.Collections.Generic;
	using Core;

	internal class AssetConjunctions
	{
		public AssetInfo asset;
		public readonly List<TreeConjunction> conjunctions = new List<TreeConjunction>();
	}
}