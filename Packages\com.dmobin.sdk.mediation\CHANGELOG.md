# Changelog

All notable changes to this package will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](http://semver.org/spec/v2.0.0.html).

## [1.1.25] - 2025-08-01

### English

#### Changed

- Updated ad loading logic between mediation and S2SBidding CostCenter for improved coordination.
- **Dependencies:** Updated `com.dmobin.sdk.core` to version 1.1.7.

### Tiếng Việt

#### Thay đổi

- Chỉnh sửa logic load quảng cáo giữa mediation và S2SBidding CostCenter để phối hợp tốt hơn.
- **<PERSON><PERSON> thuộc:** Cập nhật `com.dmobin.sdk.core` lên phiên bản 1.1.7.

## [1.1.24] - 2025-07-30

### English

#### Fixed

- Fixed crash caused by `SystemInfo.graphicsDeviceType` being null after closing NativeFull.

### Tiếng Việt

#### Sửa lỗi

- Sửa lỗi crash do `SystemInfo.graphicsDeviceType` bị null sau khi đóng NativeFull.

## [1.1.23] - 2025-07-29

### English

#### Changed

- Hide adbreak panel before invoking interstitial success callback.

### Tiếng Việt

#### Thay đổi

- Cho adbreak panel ẩn trước khi callback inter success.

## [1.1.22] - 2025-07-26

### English

#### Added

- Added remote config for AppOpen.

### Tiếng Việt

#### Thêm mới

- Thêm remote config cho AppOpen.

## [1.1.21] - 2025-07-26

### English

#### Fixed

- Fixed logic for showing native fullscreen ads in Editor.

#### Changed

- Updated dependencies:
  - com.dmobin.sdk.core: 1.1.6

### Tiếng Việt

#### Sửa lỗi

- Sửa logic hiển thị native fullscreen trên Editor.

#### Thay đổi

- Cập nhật dependencies:
  - com.dmobin.sdk.core: 1.1.6

## [1.1.20] - 2025-07-21

### English

#### Changed

- Changed the calculation method of FloorValue for CostCenter S2sBidding to use CPM.

### Tiếng Việt

#### Thay đổi

- Sửa lại cách tính FloorValue của CostCenter S2sBidding thành CPM.

## [1.1.19] - 2025-07-18

### English

#### Changed

- Removed the log in the `IsShow` function in `InterstitialControl`.
- Updated dependencies:
  - com.dmobin.sdk.core: 1.1.1

### Tiếng Việt

#### Thay đổi

- Xoá log trong hàm `IsShow` của `InterstitialControl`.
- Cập nhật dependencies:
  - com.dmobin.sdk.core: 1.1.1

## [1.1.18] - 2025-07-18

### English

#### Fixed

- Fixed banner issues for CostCenter S2sBidding.

### Tiếng Việt

#### Sửa lỗi

- Sửa lỗi banner của CostCenter S2sBidding

## [1.1.17] - 2025-07-18

### English

#### Fixed

- Fixed banner ad closed callback logic for CostCenter S2sBidding.

### Tiếng Việt

#### Sửa lỗi

- Sửa logic callback banner ad closed cho CostCenter S2sBidding

## [1.1.16] - 2025-07-18

### English

#### Fixed

- Fixed ad loading for CostCenter S2sBidding.

### Tiếng Việt

#### Sửa lỗi

- Sửa lỗi load ad CostCenter S2sBidding

## [1.1.15] - 2025-07-18

### English

#### Fixed

- Fixed banner loading logic for CostCenter S2sBidding.

### Tiếng Việt

#### Sửa lỗi

- Sửa lỗi logic load banner CostCenter S2sBidding

## [1.1.14] - 2025-07-18

### English

#### Fixed

- Fixed logic for banner load success callback.

### Tiếng Việt

#### Sửa lỗi

- Sửa logic callback load success banner

## [1.1.13] - 2025-07-18

### English

#### Fixed

- Fixed logic for banner load success callback.

### Tiếng Việt

#### Sửa lỗi

- Sửa logic callback load success banner

## [1.1.12] - 2025-07-18

### English

#### Fixed

- Fixed logic for banner load success callback.

### Tiếng Việt

#### Sửa lỗi

- Sửa logic callback load success banner

## [1.1.11] - 2025-07-18

### English

#### Fixed

- Fixed logic for destroying banners.

### Tiếng Việt

#### Sửa lỗi

- Sửa logic destroy banner

## [1.1.10] - 2025-07-18

### English

#### Fixed

- Fixed logic for showing CostCenter S2sBidding ad.

### Tiếng Việt

#### Sửa lỗi

- Sửa logic show quảng cáo CostCenter S2sBidding

## [1.1.9] - 2025-07-18

### English

#### Fixed

- Fixed logic for showing CostCenter S2sBidding ad.

### Tiếng Việt

#### Sửa lỗi

- Sửa logic show quảng cáo CostCenter S2sBidding

## [1.1.8] - 2025-07-18

### English

#### Fixed

- Fixed logic for showing CostCenter S2sBidding ad.

### Tiếng Việt

#### Sửa lỗi

- Sửa logic show quảng cáo CostCenter S2sBidding

## [1.1.7] - 2025-07-18

### English

#### Changed

- Adjusted ad load request logic.

### Tiếng Việt

#### Thay đổi

- Điều chỉnh logic request load ad

## [1.1.6] - 2025-07-18

### English

#### Changed

- Adjusted ad load request logic.

### Tiếng Việt

#### Thay đổi

- Điều chỉnh logic request load ad

## [1.1.5] - 2025-07-18

### English

#### Fixed

- Fixed ad loaded condition check for CostCenter S2sBidding.

### Tiếng Việt

#### Sửa lỗi

- Sửa điều kiện kiểm tra ad loaded cho CostCenter S2sBidding

## [1.1.4] - 2025-07-18

### English

#### Added

- Added `OnCallbackRequestSuccess` and `OnCallbackRequestFailed` functions.

### Tiếng Việt

#### Thêm mới

- Thêm các hàm `OnCallbackRequestSuccess`, `OnCallbackRequestFailed`

## [1.1.3] - 2025-07-18

### English

#### Fixed

- Fixed issue where the Load Banner function was not working.

### Tiếng Việt

#### Sửa lỗi

- Sửa lỗi hàm Load Banner không hoạt động

## [1.1.2] - 2025-07-17

### English

#### Added

- Added load ad success and failed callbacks for CostCenter S2sBidding.

### Tiếng Việt

#### Thêm mới

- Thêm callback load ad success và failed cho CostCenter S2sBidding

## [1.1.1] - 2025-07-17

### English

#### Added

- Added IsEnableCostcenterS2sBididing to AdSettingsWindow

### Tiếng Việt

#### Thêm mới

- Thêm IsEnableCostcenterS2sBididing vào AdSettingsWindow

## [1.1.0] - 2025-07-16

### English

#### Changed

- Updated Cost Center S2sBidding adapter.
- Updated dependencies:
  - com.dmobin.sdk.core: 1.1.0

### Tiếng Việt

#### Thay đổi

- Cập nhật adapter Cost Center S2sBidding.
- Cập nhật dependencies:
  - com.dmobin.sdk.core: 1.1.0

## [1.0.18] - 2025-07-09

### English

#### Fixed

- Fixed bug where the ad editor did not automatically refresh when renaming an adapter

### Tiếng Việt

#### Sửa lỗi

- Sửa lỗi ad editor không tự động refresh khi đổi tên adapter

## [1.0.17] - 2025-07-09

### English

#### Fixed

- Fixed bug with fetching default value from remote config

### Tiếng Việt

#### Sửa lỗi

- Sửa lỗi lấy giá trị default trên remote config

## [1.0.16] - 2025-07-09

### English

#### Fixed

- Fixed bug where remote mediation config could not be fetched

### Tiếng Việt

#### Sửa lỗi

- Sửa lỗi không lấy được remote mediation

## [1.0.15] - 2025-07-09

### English

#### Fixed

- Fixed bug with receiving remote config for ad formats

### Tiếng Việt

#### Sửa lỗi

- Sửa lỗi nhận remote config các định dạng ad

## [1.0.14] - 2025-07-09

### English

#### Fixed

- Fixed bug with receiving remote config for ad formats

### Tiếng Việt

#### Sửa lỗi

- Sửa lỗi nhận remote config các định dạng ad

## [1.0.13] - 2025-07-08

### English

#### Added

- Added manual banner loading function in BannerControl

### Tiếng Việt

#### Thêm mới

- Thêm hàm load banner thủ công trong BannerControl

## [1.0.12] - 2025-07-08

### English

#### Added

- Added MREC banner ID

### Tiếng Việt

#### Thêm mới

- Thêm banner ID MREC

## [1.0.11] - 2025-07-08

### English

#### Added

- Added offset adjustment for banner positioning

### Tiếng Việt

#### Thêm mới

- Thêm offset điều chỉnh vị trí cho banner

## [1.0.10] - 2025-07-06

### English

#### Changed

- Fixed remote config loading logic

### Tiếng Việt

#### Thay đổi

- Sửa logic chờ load từ remote config

## [1.0.8] - 2025-07-03

### English

#### Added

- Added hide banner by ID functionality

### Tiếng Việt

#### Thêm mới

- Thêm chức năng ẩn banner theo ID

## [1.0.7] - 2025-07-03

### English

#### Changed

- Fixed dynamic banner display logic for multiple positions
- Updated dependencies:
  - com.dmobin.sdk.core: 1.0.7

### Tiếng Việt

#### Thay đổi

- Sửa logic show banner linh động ở nhiều vị trí
- Cập nhật dependencies:
  - com.dmobin.sdk.core: 1.0.7

## [1.0.6] - 2025-06-27

### English

#### Added

- Added Undo/Redo functionality for editor operations

#### Changed

- Fixed AppResumeChecker implementation
- Updated dependencies:
  - com.dmobin.sdk.core: 1.0.6

### Tiếng Việt

#### Thêm mới

- Thêm tính năng Undo/Redo cho editor

#### Thay đổi

- Sửa lại AppResumeChecker
- Cập nhật dependencies:
  - com.dmobin.sdk.core: 1.0.6

## [1.0.4] - 2025-06-20

### English

#### Changed

- Restored static modifier for OnAllShowCallBack action
- Moved OnCloseCallback action outside of editor scope

### Tiếng Việt

#### Thay đổi

- Đặt lại static cho action OnAllShowCallBack
- Đưa action OnCloseCallback ra khỏi editor

## [1.0.3] - 2025-06-20

### English

#### Changed

- Removed static modifier from OnAllShowCallBack action for better instance-based control

### Tiếng Việt

#### Thay đổi

- Bỏ static cho action OnAllShowCallBack để kiểm soát tốt hơn dựa trên instance

## [1.0.2] - 2025-06-19

### English

#### Added

- Added remote config for adjusting banner size on tablet devices
- Added references to DSDK.Utils

#### Changed

- Updated dependencies:
  - com.dmobin.sdk.core: 1.0.2

### Tiếng Việt

#### Thêm mới

- Thêm remote config điều chỉnh size banner cho tablet
- Thêm references tới DSDK.Utils

#### Thay đổi

- Cập nhật dependencies:
  - com.dmobin.sdk.core: 1.0.2

## [1.0.1] - 2025-06-18

### English

#### Fixed

- Fixed issue with displaying editor ads.

#### Added

- Added banner ad mediation backfill: if adaptive banner fails to load, fallback to another banner type.

#### Changed

- Moved some action callbacks to static for better accessibility and consistency.

### Tiếng Việt

#### Sửa lỗi

- Sửa lỗi hiển thị quảng cáo trong chế độ Editor.

#### Thêm mới

- Thêm cơ chế backfill cho banner ad mediation: nếu banner adaptive không load được sẽ tự động chuyển sang loại banner khác.

#### Thay đổi

- Đưa một số action callback sang static để dễ sử dụng và đồng bộ hơn.

## [1.0.0] - 2025-04-11

### English

#### Added

- Initial stable release of the Mediation package
- Unified API for multiple mediation providers
- Support for all major ad formats: banner, interstitial, rewarded, native
- Ad revenue tracking and analytics integration
- Remote configuration for ad settings
- Automatic initialization and lifecycle management
- Editor preview mode for testing without ads
- Support for MAX, AdMob, LevelPlay, and Yandex ad networks

#### Changed

- Refactored the mediation architecture to use the adapter pattern
- Improved ad loading and caching mechanisms
- Enhanced placement management and ad rotation

#### Fixed

- None (initial release).

### Tiếng Việt

#### Thêm mới

- Phát hành phiên bản ổn định đầu tiên của gói Mediation
- API thống nhất cho nhiều nhà cung cấp trung gian
- Hỗ trợ tất cả các định dạng quảng cáo chính: banner, interstitial, rewarded, native
- Theo dõi doanh thu quảng cáo và tích hợp phân tích
- Cấu hình từ xa cho cài đặt quảng cáo
- Tự động khởi tạo và quản lý vòng đời
- Chế độ xem trước trình chỉnh sửa để kiểm tra mà không cần quảng cáo
- Hỗ trợ các mạng quảng cáo MAX, AdMob, LevelPlay và Yandex

#### Thay đổi

- Cấu trúc lại kiến trúc trung gian để sử dụng mẫu adapter
- Cải thiện cơ chế tải và lưu trữ quảng cáo
- Nâng cao quản lý vị trí và luân chuyển quảng cáo

#### Sửa lỗi

- Không có (bản phát hành đầu tiên).
