{"name": "com.dmobin.sdk.core", "version": "1.1.7", "displayName": "Dmobin - Core", "description": "Core foundation of the Dmobin SDK providing essential services for all modules including lifecycle management, logging, remote configuration, internet connectivity, and event handling", "author": {"name": "Dmobin - Core", "url": "https://dmobin.com"}, "keywords": ["Dmobin"], "repository": {"type": "git", "url": "https://git.dmobin.studio/dmobin-gdk/dmobin-gdk.git", "directory": "Packages/com.dmobin.sdk.core"}, "publishConfig": {"registry": "https://upm.dmobin.studio"}, "dependencies": {"com.unity.nuget.newtonsoft-json": "3.2.1", "com.dmobin.sdk.utils": "1.0.7", "com.dmobin.sdk.scenesystem": "1.0.4", "com.dmobin.game.manager": "1.0.5"}}