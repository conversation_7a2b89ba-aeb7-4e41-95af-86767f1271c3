using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading;
using GoogleMobileAds.Api;
using UnityEngine;

namespace DSDK.Mediation.GoogleMobileAds.Native
{
    public class NativeAdController : MonoBehaviour
    {
        public static NativeAdController Instance { get; private set; }
        public NativeAd NativeAd => _nativeAd;

        private static SynchronizationContext _synchronizationContext;
        private static int _unityMainThreadId;

        private NativeAd _nativeAd;
        private string[] _adUnitIds;
        private int _currentAdUnitIndex;
        public Action<NativeAd> OnAdLoaded;
        public Action<string> OnAdFailedToLoad;
        public Action<AdValue> OnAdPaid;

        public bool IsLoaded => _nativeAd != null;
        public float RefreshRate = 20; // in seconds
        public int MaxRetries = 3;

        public List<NativeAdView> NativeAdViews = new List<NativeAdView>();

        private Coroutine _refreshCoroutine;

        private void Awake()
        {
            Instance = this;
            SetUnityMainThreadSynchronizationContext();
        }

        private void OnDestroy()
        {
            if (_refreshCoroutine != null)
            {
                StopCoroutine(_refreshCoroutine);
            }
            if (_nativeAd != null)
            {
                _nativeAd.Destroy();
            }
        }

        public void Init(string[] adUnitIds)
        {
            _adUnitIds = adUnitIds;
        }

        public void LoadAd()
        {
            Load();
        }

        private void Load(int retries = 0)
        {
            var adUnitId = GetAdUnitId();

            IEnumerator Retry()
            {
                retries++;
                yield return new WaitForSeconds((retries + 1) * 2);
                Load(retries);
            }

            void HandleAdFailedToLoad(object sender, AdFailedToLoadEventArgs args)
            {
                Debug.Log("Native ad failed to load: " + args.LoadAdError.GetMessage());
                if (retries < MaxRetries)
                {
                    Retry();
                    return;
                }
                RaiseAction(() => OnAdFailedToLoad?.Invoke(args.LoadAdError.GetMessage()));
            }

            void HandleNativeAdLoaded(object sender, NativeAdEventArgs args)
            {
                _nativeAd = args.nativeAd;
                _nativeAd.OnPaidEvent += HandleAdPaid;
                foreach (var nativeAdView in NativeAdViews)
                {
                    nativeAdView.SetNativeAd(_nativeAd);
                }
                StartRefreshCoroutine(RefreshRate);
                RaiseAction(() => OnAdLoaded?.Invoke(_nativeAd));
            }

            void HandleAdPaid(object sender, AdValueEventArgs args)
            {
                RaiseAction(() => OnAdPaid?.Invoke(args.AdValue));
            }

            AdLoader adLoader = new AdLoader.Builder(adUnitId).ForNativeAd().Build();
            adLoader.OnNativeAdLoaded += HandleNativeAdLoaded;
            adLoader.OnAdFailedToLoad += HandleAdFailedToLoad;
            adLoader.LoadAd(new AdRequest());
        }

        internal static void RaiseAction(Action action)
        {
            if (action == null)
            {
                return;
            }

            if (_synchronizationContext == null ||
                Thread.CurrentThread.ManagedThreadId == _unityMainThreadId)
            {
                action();
                return;
            }

            _synchronizationContext.Post((state) => { action(); }, action);
        }

        internal static void SetUnityMainThreadSynchronizationContext()
        {
            _synchronizationContext = SynchronizationContext.Current;
            _unityMainThreadId = Thread.CurrentThread.ManagedThreadId;
        }

        private string GetAdUnitId()
        {
            var adUnitId = _adUnitIds[_currentAdUnitIndex];
            _currentAdUnitIndex++;
            if (_currentAdUnitIndex >= _adUnitIds.Length)
            {
                _currentAdUnitIndex = 0;
            }
            return adUnitId;
        }

        public void ShowAllNativeAdViews()
        {
            foreach (var nativeAdView in NativeAdViews)
            {
                nativeAdView.ShowContent();
            }
        }

        public void HideAllNativeAdViews()
        {
            foreach (var nativeAdView in NativeAdViews)
            {
                nativeAdView.HideContent();
            }
        }

        public void HideAllNativeAdViews(NativeAdOverlayObject nativeAdOverlayObject)
        {
            foreach (var nativeAdView in NativeAdViews)
            {
                nativeAdView.HideContent(nativeAdOverlayObject);
            }
        }

        private void StartRefreshCoroutine(float delayInSeconds)
        {
            // Dừng coroutine cũ nếu nó đang chạy để bắt đầu một chu trình mới
            if (_refreshCoroutine != null)
            {
                StopCoroutine(_refreshCoroutine);
            }
            _refreshCoroutine = StartCoroutine(RefreshAd(delayInSeconds));
        }

        private IEnumerator RefreshAd(float delay)
        {
            Debug.Log($"Scheduling next ad request in {delay} seconds.");
            yield return new WaitForSeconds(delay);
            LoadAd(); // Gọi tải quảng cáo mới
        }
    }
}
