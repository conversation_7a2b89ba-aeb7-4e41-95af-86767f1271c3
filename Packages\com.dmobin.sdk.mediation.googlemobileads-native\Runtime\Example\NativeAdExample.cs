using System;
using UnityEngine;
using GoogleMobileAds.Api;

namespace DSDK.Mediation.GoogleMobileAds.Native.Example
{
    public class NativeAdExample : MonoBehaviour
    {
        [Header("Ad Unit IDs")]
        public string[] adUnitIds = new string[]
        {
            "ca-app-pub-3940256099942544/2247696110", // Test ad unit ID
            "ca-app-pub-3940256099942544/2247696110", // Test ad unit ID
            "ca-app-pub-3940256099942544/2247696110"  // Test ad unit ID
        };

        private void Start()
        {
            // Initialize Google Mobile Ads SDK
            MobileAds.Initialize(initStatus =>
            {
                Debug.Log("Google Mobile Ads SDK initialized.");
                InitializeNativeAds();
            });
        }

        private void InitializeNativeAds()
        {
            if (NativeAdController.Instance != null)
            {
                // Subscribe to events
                NativeAdController.Instance.OnAdLoaded += OnAdLoaded;
                NativeAdController.Instance.OnAdFailedToLoad += OnAdFailedToLoad;
                NativeAdController.Instance.OnAdPaid += OnAdPaid;
                NativeAdController.Instance.OnAdShow += OnAdShow;
                NativeAdController.Instance.OnAdHide += OnAdHide;

                // Initialize with ad unit IDs
                NativeAdController.Instance.Init(adUnitIds);
            }
        }

        private void OnAdLoaded(NativeAd nativeAd)
        {
            Debug.Log("Native ad loaded successfully!");
        }

        private void OnAdFailedToLoad(string error)
        {
            Debug.LogError($"Native ad failed to load: {error}");
        }

        private void OnAdPaid(AdValue adValue, string location)
        {
            Debug.Log($"Native ad paid: {adValue.Value} {adValue.CurrencyCode} for location: {location}");
        }

        private void OnAdShow(string location)
        {
            Debug.Log($"Native ad shown at location: {location}");
        }

        private void OnAdHide(string location)
        {
            Debug.Log($"Native ad hidden at location: {location}");
        }

        private void OnDestroy()
        {
            if (NativeAdController.Instance != null)
            {
                // Unsubscribe from events
                NativeAdController.Instance.OnAdLoaded -= OnAdLoaded;
                NativeAdController.Instance.OnAdFailedToLoad -= OnAdFailedToLoad;
                NativeAdController.Instance.OnAdPaid -= OnAdPaid;
                NativeAdController.Instance.OnAdShow -= OnAdShow;
                NativeAdController.Instance.OnAdHide -= OnAdHide;
            }
        }
    }
}
