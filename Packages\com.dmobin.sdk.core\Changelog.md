# Changelog

All notable changes to this package will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](http://semver.org/spec/v2.0.0.html).

## [1.1.7] - 2025-08-01

### English

#### Added

- Added new ad mediation type: `adx`.

### Tiếng Việt

#### Thêm mới

- Thêm loại ad mediation mới: `adx`.

## [1.1.6] - 2025-07-25

### English

#### Changed

- **Dependencies:** Updated `com.dmobin.sdk.scenesystem` to version 1.0.4.

### Tiếng Việt

#### Thay đổi

- **Phụ thuộc:** Cập nhật `com.dmobin.sdk.scenesystem` lên phiên bản 1.0.4.

## [1.1.5] - 2025-07-25

### English

#### Added

- Added new ad type: `rewarded_inter` in `AdTypes` to support rewarded interstitial ads.

### Tiếng Việt

#### Thêm

- Thêm loại quảng cáo mới: `rewarded_inter` trong `AdTypes` để hỗ trợ quảng cáo xen kẽ có thưởng.

## [1.1.4] - 2025-07-23

### English

#### Fixed

- **Texture Tools:** Fixed lag when selecting multiple files.

#### Changed

- **Dependencies:** Updated `com.dmobin.sdk.scenesystem` to version 1.0.2.

### Tiếng Việt

#### Sửa lỗi

- **Công cụ Texture:** Sửa lag khi chọn nhiều file.

#### Thay đổi

- **Phụ thuộc:** Cập nhật `com.dmobin.sdk.scenesystem` lên phiên bản 1.0.2.

## [1.1.3] - 2025-07-23

### English

#### Added

- **Core:** Added option to set target frame rate for the application.

### Tiếng Việt

#### Thêm mới

- **Core:** Thêm tuỳ chọn đặt target frame rate cho ứng dụng.

## [1.1.2] - 2025-07-22

### English

#### Added

- **Texture Tools:** Added "AUTO-OPTIMIZATION" button to quickly optimize selected textures for mobile (auto resize to multiples of 4, apply optimal import settings).
- **Texture Tools:** Improved performance by caching selected textures and reducing lag with large selections.
- **Texture Tools:** Enhanced UI with section headers, help boxes, and highlighted action buttons.
- **Audio Tools:** Added "AUTO-OPTIMIZATION" button to batch optimize selected audio files based on duration (auto set load type, compression, force to mono, and quality).
- **Audio Tools:** Added options for "Force To Mono" and "Audio Quality" in the UI.

#### Changed

- **Texture Tools:** Refactored and organized code for better maintainability and user experience.
- **Audio Tools:** Improved selection logic to support folders and multiple files.

### Tiếng Việt

#### Thêm mới

- **Công cụ Texture:** Thêm nút "AUTO-OPTIMIZATION" để tối ưu nhanh các texture đã chọn cho mobile (tự động resize chia hết cho 4, áp dụng import settings tối ưu).
- **Công cụ Texture:** Tăng hiệu năng bằng cách cache selection, giảm lag khi chọn nhiều texture.
- **Công cụ Texture:** Cải thiện giao diện với tiêu đề, hộp trợ giúp, nút nổi bật.
- **Công cụ Audio:** Thêm nút "AUTO-OPTIMIZATION" để tối ưu hàng loạt file audio theo thời lượng (tự động chọn load type, nén, force mono, chất lượng).
- **Công cụ Audio:** Thêm tuỳ chọn "Force To Mono" và "Audio Quality" trên giao diện.

#### Thay đổi

- **Công cụ Texture:** Tổ chức lại code, nâng cao trải nghiệm người dùng.
- **Công cụ Audio:** Cải thiện logic chọn file, hỗ trợ chọn thư mục và nhiều file.

## [1.1.1] - 2025-07-18

### English

#### Changed

- Removed redundant log from `UnityUtils`.

### Tiếng Việt

#### Thay đổi

- Xoá log thừa của `UnityUtils`.

## [1.1.0] - 2025-07-16

### English

#### Added

- Added tracking and configuration for CostCenter S2sBidding Mediation.

#### Changed

- Updated dependencies:
  - com.dmobin.sdk.scenesystem: 1.0.1

### Tiếng Việt

#### Thêm mới

- Thêm tracking, cấu hình cho CostCenter S2sBidding Mediation.

#### Thay đổi

- Cập nhật dependencies:
  - com.dmobin.sdk.scenesystem: 1.0.1

## [1.0.11] - 2025-07-11

### English

#### Changed

- Updated dependencies:
  - com.dmobin.game.manager: 1.0.5

### Tiếng Việt

#### Thay đổi

- Cập nhật dependencies:
  - com.dmobin.game.manager: 1.0.5

## [1.0.10] - 2025-07-11

### English

#### Changed

- Updated dependencies:
  - com.dmobin.sdk.utils: 1.0.7

### Tiếng Việt

#### Thay đổi

- Cập nhật dependencies:
  - com.dmobin.sdk.utils: 1.0.7

## [1.0.9] - 2025-07-11

### English

#### Added

- Added Facebook integration to GDK

#### Changed

- Updated dependencies:
  - com.dmobin.sdk.utils: 1.0.6

### Tiếng Việt

#### Thêm mới

- Thêm Facebook vào GDK

#### Thay đổi

- Cập nhật dependencies:
  - com.dmobin.sdk.utils: 1.0.6

## [1.0.8] - 2025-07-05

### English

#### Changed

- Updated dependencies:
  - com.dmobin.sdk.utils: 1.0.5
  - com.dmobin.game.manager: 1.0.3

### Tiếng Việt

#### Thay đổi

- Cập nhật dependencies:
  - com.dmobin.sdk.utils: 1.0.5
  - com.dmobin.game.manager: 1.0.3

## [1.0.7] - 2025-07-01

### English

#### Added

- Added Native Share package integration

### Tiếng Việt

#### Thêm mới

- Thêm tích hợp gói Native Share

## [1.0.6] - 2025-06-24

### English

#### Added

- Added CameraController component for camera management
- Added support for ProCamera2D integration
- Added camera follow functionality with customizable parameters
- Added camera shake support through ProCamera2DShake

### Tiếng Việt

#### Thêm mới

- Thêm component CameraController để quản lý camera
- Thêm hỗ trợ tích hợp ProCamera2D
- Thêm chức năng camera follow với các tham số tùy chỉnh
- Thêm hỗ trợ camera shake thông qua ProCamera2DShake

## [1.0.5] - 2025-06-23

### English

#### Added

- Added basic events in EventConstants

### Tiếng Việt

#### Thêm mới

- Thêm một số event cơ bản trong EventConstants

## [1.0.4] - 2025-06-23

### English

#### Added

- Added MixSkinManager for Spine2D to SDKCore

### Tiếng Việt

#### Thêm mới

- Thêm MixSkinManager của Spine2D vào SDKCore

## [1.0.3] - 2025-06-23

### English

#### Added

- Added basic events in EventConstants

#### Removed

- Removed dependencies:
  - com.dmobin.sdk.uisystem
  - com.dmobin.sdk.data

### Tiếng Việt

#### Thêm mới

- Thêm một số event cơ bản trong EventConstants

#### Đã xóa

- Xoá phụ thuộc dependencies trong package:
  - com.dmobin.sdk.uisystem
  - com.dmobin.sdk.data

## [1.0.2] - 2025-06-19

### English

#### Changed

- Updated dependencies:
  - com.dmobin.sdk.utils: 1.0.2
  - com.dmobin.sdk.uisystem: 1.0.2

### Tiếng Việt

#### Thay đổi

- Cập nhật dependencies:
  - com.dmobin.sdk.utils: 1.0.2
  - com.dmobin.sdk.uisystem: 1.0.2

## [1.0.1] - 2025-06-13

### English

#### Added

- Added new commonly used events for better game flow control
- Added event handlers for scene loading and unloading
- Added events for game state changes
- Added events for UI interactions

#### Fixed

- Fixed memory leak in event system
- Fixed null reference exception in singleton pattern
- Fixed race condition in coroutine management
- Fixed serialization issues with custom data types

### Tiếng Việt

#### Thêm mới

- Thêm các sự kiện thường dùng để kiểm soát luồng game tốt hơn
- Thêm các xử lý sự kiện cho việc tải và dỡ scene
- Thêm các sự kiện cho thay đổi trạng thái game
- Thêm các sự kiện cho tương tác UI

#### Đã sửa

- Sửa lỗi rò rỉ bộ nhớ trong hệ thống sự kiện
- Sửa lỗi null reference trong mẫu Singleton
- Sửa lỗi race condition trong quản lý coroutine
- Sửa lỗi tuần tự hóa với các kiểu dữ liệu tùy chỉnh

## [1.0.0] - 2025-04-11

### English

#### Added

- First release.
- Main core for project.
- New utility functions for common operations.
- Singleton pattern implementation for easy access to managers.
- Event system for decoupled communication between components.
- Remote configuration system for dynamic game settings.
- Logging system with multiple channels and log levels.
- Utility classes for string, math, and collection operations.
- Extension methods for Unity components and common data types.
- Serialization helpers for JSON and binary data.
- Coroutine management utilities.

#### Fixed

- None (initial release).

### Tiếng Việt

#### Thêm mới

- Bản release đầu tiên.
- Core chính cho dự án.
- Các hàm tiện ích mới cho các thao tác thông thường.
- Triển khai mẫu Singleton để dễ dàng truy cập các manager.
- Hệ thống sự kiện cho giao tiếp phi kết nối giữa các thành phần.
- Hệ thống cấu hình từ xa cho các cài đặt game động.
- Hệ thống ghi log với nhiều kênh và cấp độ log.
- Các lớp tiện ích cho các thao tác chuỗi, toán học và tập hợp.
- Các phương thức mở rộng cho các thành phần Unity và các kiểu dữ liệu phổ biến.
- Các tiện ích hỗ trợ tuần tự hóa cho dữ liệu JSON và nhị phân.
- Các tiện ích quản lý Coroutine.

#### Đã sửa

- Không có (bản phát hành đầu tiên).
