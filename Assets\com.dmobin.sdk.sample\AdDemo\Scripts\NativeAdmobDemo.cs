using DSDK.Extensions.NativeAds.Components;
using DSDK.Extensions.NativeAds.Controllers;
using DSDK.Mediation.AdmobNative.Api;
using DSDK.Mediation.GoogleMobileAds.Native;
using DSDK.Mediation.NativeAds.Controllers;
using GoogleMobileAds.Api;
using UnityEngine;

public class NativeAdmobDemo : MonoBehaviour
{
    [SerializeField] private NativeBannerController nativeBannerController;
    [SerializeField] private NativeImmersiveController nativeImmersiveController;
    [SerializeField] private NativeImmersiveView nativeImmersiveView;
    [SerializeField] private NativeMultiIdBannerController nativeMultiIdBannerController;

    private const string AdUnitId1 = "ca-app-pub-3073237800540363/9224036323";
    private const string AdUnitId2 = "ca-app-pub-3073237800540363/6231427548";

    private void Awake()
    {
        RequestConfiguration requestConfiguration = new RequestConfiguration();
        requestConfiguration.TestDeviceIds.Add("B8D3491548F2F3B2F313E8C9785E985F");
        MobileAds.SetRequestConfiguration(requestConfiguration);

        nativeBannerController.Init(new NativeBannerOption()
        {
            Position = NativeBanner.Position.Left,
            Height = 300,
            DelayCollapseButton = 10,
            PressCollapseHideAd = true,
        }, new string[] { AdUnitId1 });
        nativeImmersiveController.SetAdUnitIds(new string[] { AdUnitId1 });
        nativeMultiIdBannerController.Init(new NativeMultiIdBannerConfig(), AdUnitId1, AdUnitId2);

        nativeMultiIdBannerController.UseMultipleExpanded(false);
    }

    private void Start()
    {
        NativeAdController.Instance.Init(new string[] { AdUnitId1, AdUnitId2 });
    }

    public void ShowBanner()
    {
        nativeBannerController.Show(true);
    }

    public void HideBanner()
    {
        nativeBannerController.Hide();
    }

    public void ExpandBanner()
    {
        nativeBannerController.SetExpanded(true);
    }

    public void ShowImmersive()
    {
        // _nativeImmersiveView.Show();
        nativeImmersiveView.gameObject.SetActive(true);
    }

    public void HideImmersive()
    {
        // _nativeImmersiveView.Hide();
        nativeImmersiveView.gameObject.SetActive(false);
    }

    public void ShowMultiIdBanner()
    {
        nativeMultiIdBannerController.Show();
    }

    public void HideMultiIdBanner()
    {
        // nativeMultiIdBannerController.Hide();
        nativeMultiIdBannerController.Destroy();
    }
}
