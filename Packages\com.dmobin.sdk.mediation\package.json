{"name": "com.dmobin.sdk.mediation", "version": "1.1.25", "displayName": "Dmobin - Core - Mediation", "description": "Unified advertising solution that integrates multiple ad networks (MAX, AdMob, LevelPlay, Yandex) with a consistent API, automated ad loading, revenue tracking, and advanced configuration options", "author": {"name": "Dmobin - Core", "url": "https://dmobin.com"}, "keywords": ["Dmobin"], "repository": {"type": "git", "url": "https://git.dmobin.studio/dmobin-gdk/dmobin-gdk.git", "directory": "Packages/com.dmobin.sdk.mediation"}, "publishConfig": {"registry": "https://upm.dmobin.studio"}, "dependencies": {"com.dmobin.sdk.core": "1.1.7", "com.unity.ads.ios-support": "1.0.0"}}