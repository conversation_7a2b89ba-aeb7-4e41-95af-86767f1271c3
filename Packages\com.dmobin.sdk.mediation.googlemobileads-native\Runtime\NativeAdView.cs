using System.Collections;
using System.Collections.Generic;
using GoogleMobileAds.Api;
using UnityEngine;
using UnityEngine.UI;

namespace DSDK.Mediation.GoogleMobileAds.Native
{
    public class NativeAdView : MonoBehaviour
    {
        [SerializeField] private GameObject content;
        [SerializeField] private string location; // Unique identifier for this ad view
        [SerializeField] private string adUnitId; // Optional: specific ad unit for this view

        [SerializeField] private Text headline;
        [SerializeField] private Text body;
        [SerializeField] private Text callToAction;
        [SerializeField] private Text advertiser;
        [SerializeField] private RawImage icon;
        [SerializeField] private RawImage coverImage;
        [SerializeField] private RawImage adchoice;
        [SerializeField] private GameObject[] hideObjects;

        private NativeAd _currentNativeAd;

        // Public properties
        public string Location => location;
        public NativeAd GetCurrentNativeAd() => _currentNativeAd;

        private void Start()
        {
            Init();
        }

        private void Init()
        {
            if (NativeAdController.Instance == null) return;

            // Generate location if not set
            if (string.IsNullOrEmpty(location))
            {
                location = gameObject.name + "_" + GetInstanceID();
            }

            // Try to get an ad for this location
            var ad = NativeAdController.Instance.GetAdForLocation(location);
            if (ad != null)
            {
                SetNativeAd(ad);
            }
            else
            {
                // Listen for new ads being loaded
                NativeAdController.Instance.OnAdLoaded += OnAdLoadedHandler;
                HideContent();
            }

            NativeAdController.Instance.NativeAdViews.Add(this);
        }

        private void OnAdLoadedHandler(NativeAd nativeAd)
        {
            // Only set the ad if we don't already have one
            if (_currentNativeAd == null)
            {
                var ad = NativeAdController.Instance.GetAdForLocation(location);
                if (ad != null)
                {
                    SetNativeAd(ad);
                    // Unsubscribe since we now have an ad
                    NativeAdController.Instance.OnAdLoaded -= OnAdLoadedHandler;
                }
            }
        }

        public void HideContent()
        {
            Debug.Log("HideContent");
            content.SetActive(false);
            SetObjectsActive(true);

            // Trigger OnAdHide event
            if (NativeAdController.Instance != null)
            {
                NativeAdController.Instance.TriggerAdHide(location);
            }
        }

        public void ShowContent()
        {
            Debug.Log("ShowContent");
            content.SetActive(true);
            SetObjectsActive(false);

            // Trigger OnAdShow event
            if (NativeAdController.Instance != null)
            {
                NativeAdController.Instance.TriggerAdShow(location);
            }
        }

        public void HideContent(NativeAdOverlayObject nativeAdOverlayObject)
        {
            // Check if the overlay object is on a different GameObject
            if (nativeAdOverlayObject.gameObject == this.gameObject)
            {
                return;
            }

            // Check if the overlay object is a parent of the current object
            if (IsParentObject(nativeAdOverlayObject.gameObject, this.gameObject))
            {
                Debug.Log("NativeAdOverlayObject is a parent of NativeAdView, skipping hide content");
                return;
            }

            // Check if the overlay object's rect overlaps with this NativeAdView's rect
            if (IsRectOverlapping(nativeAdOverlayObject.GetComponent<RectTransform>(), GetComponent<RectTransform>()))
            {
                content.SetActive(false);
                SetObjectsActive(true);
            }
        }

        private bool IsRectOverlapping(RectTransform rect1, RectTransform rect2)
        {
            if (rect1 == null || rect2 == null)
            {
                Debug.LogWarning("One of the RectTransforms is null");
                return false;
            }

            // Check if both RectTransforms are in the same Canvas
            Canvas canvas1 = rect1.GetComponentInParent<Canvas>();
            Canvas canvas2 = rect2.GetComponentInParent<Canvas>();
            
            if (canvas1 != canvas2)
            {
                Debug.LogWarning("RectTransforms are in different Canvases, cannot compare accurately");
                return false;
            }

            // Get the corners in local space relative to the Canvas
            Vector3[] corners1 = new Vector3[4];
            Vector3[] corners2 = new Vector3[4];
            
            rect1.GetLocalCorners(corners1);
            rect2.GetLocalCorners(corners2);

            // Convert to screen space coordinates
            Vector2[] screenCorners1 = new Vector2[4];
            Vector2[] screenCorners2 = new Vector2[4];

            for (int i = 0; i < 4; i++)
            {
                // Convert local corners to world position
                Vector3 worldPos1 = rect1.TransformPoint(corners1[i]);
                Vector3 worldPos2 = rect2.TransformPoint(corners2[i]);
                
                // Convert to screen coordinates
                screenCorners1[i] = RectTransformUtility.WorldToScreenPoint(Camera.main, worldPos1);
                screenCorners2[i] = RectTransformUtility.WorldToScreenPoint(Camera.main, worldPos2);
            }

            // Debug logging
            Debug.Log($"Rect1 corners: {screenCorners1[0]}, {screenCorners1[1]}, {screenCorners1[2]}, {screenCorners1[3]}");
            Debug.Log($"Rect2 corners: {screenCorners2[0]}, {screenCorners2[1]}, {screenCorners2[2]}, {screenCorners2[3]}");

            bool isOverlapping = IsScreenRectOverlapping(screenCorners1, screenCorners2);
            Debug.Log($"Is overlapping: {isOverlapping}");
            
            return isOverlapping;
        }

        private bool IsWorldRectOverlapping(Vector3[] corners1, Vector3[] corners2)
        {
            // Simple world space overlap check using bounds
            Bounds bounds1 = new Bounds();
            Bounds bounds2 = new Bounds();

            for (int i = 0; i < 4; i++)
            {
                bounds1.Encapsulate(corners1[i]);
                bounds2.Encapsulate(corners2[i]);
            }

            return bounds1.Intersects(bounds2);
        }

        private bool IsScreenRectOverlapping(Vector2[] corners1, Vector2[] corners2)
        {
            // Calculate bounding rectangles in screen space
            float minX1 = Mathf.Min(corners1[0].x, corners1[1].x, corners1[2].x, corners1[3].x);
            float maxX1 = Mathf.Max(corners1[0].x, corners1[1].x, corners1[2].x, corners1[3].x);
            float minY1 = Mathf.Min(corners1[0].y, corners1[1].y, corners1[2].y, corners1[3].y);
            float maxY1 = Mathf.Max(corners1[0].y, corners1[1].y, corners1[2].y, corners1[3].y);

            float minX2 = Mathf.Min(corners2[0].x, corners2[1].x, corners2[2].x, corners2[3].x);
            float maxX2 = Mathf.Max(corners2[0].x, corners2[1].x, corners2[2].x, corners2[3].x);
            float minY2 = Mathf.Min(corners2[0].y, corners2[1].y, corners2[2].y, corners2[3].y);
            float maxY2 = Mathf.Max(corners2[0].y, corners2[1].y, corners2[2].y, corners2[3].y);

            // Debug logging
            Debug.Log($"Rect1 bounds: ({minX1}, {minY1}) to ({maxX1}, {maxY1})");
            Debug.Log($"Rect2 bounds: ({minX2}, {minY2}) to ({maxX2}, {maxY2})");

            // Check for overlap - rectangles overlap if they are NOT separated
            bool isSeparated = maxX1 < minX2 || maxX2 < minX1 || maxY1 < minY2 || maxY2 < minY1;
            bool isOverlapping = !isSeparated;
            
            Debug.Log($"Is separated: {isSeparated}, Is overlapping: {isOverlapping}");
            
            return isOverlapping;
        }

        private bool IsParentObject(GameObject potentialParent, GameObject child)
        {
            if (potentialParent == null || child == null)
            {
                return false;
            }

            Transform current = child.transform;
            while (current.parent != null)
            {
                if (current.parent.gameObject == potentialParent)
                {
                    return true;
                }
                current = current.parent;
            }

            return false;
        }

        public void SetNativeAd(NativeAd nativeAd)
        {
            _currentNativeAd = nativeAd;

            if (headline != null)
            {
                headline.text = nativeAd.GetHeadlineText();
                if (!nativeAd.RegisterHeadlineTextGameObject(headline.gameObject))
                {
                    headline.gameObject.SetActive(false);
                    Debug.LogError("Failed to register headline text game object");
                }
            }

            if (body != null)
            {
                body.text = nativeAd.GetBodyText();
                if (!nativeAd.RegisterBodyTextGameObject(body.gameObject))
                {
                    body.gameObject.SetActive(false);
                    Debug.LogError("Failed to register body text game object");
                }
            }

            if (callToAction != null)
            {
                callToAction.text = nativeAd.GetCallToActionText();
                if (!nativeAd.RegisterCallToActionGameObject(callToAction.gameObject))
                {
                    callToAction.gameObject.SetActive(false);
                    Debug.LogError("Failed to register call to action game object");
                }
            }

            if (icon != null)
            {
                Texture2D texture = nativeAd.GetIconTexture();
                if (texture != null)
                {
                    icon.texture = texture;
                }

                if (!nativeAd.RegisterIconImageGameObject(icon.gameObject))
                {
                    icon.gameObject.SetActive(false);
                    Debug.LogError("Failed to register icon image game object");
                }
            }

            if (coverImage != null)
            {
                List<Texture2D> textures = nativeAd.GetImageTextures();
                if (textures.Count > 0)
                {
                    coverImage.texture = textures[0];
                }

                if (nativeAd.RegisterImageGameObjects(new List<GameObject> { coverImage.gameObject }) == 0)
                {
                    coverImage.gameObject.SetActive(false);
                    Debug.LogError("Failed to register cover image game object");
                }
            }

            if (advertiser != null)
            {
                advertiser.text = nativeAd.GetAdvertiserText();
                if (!nativeAd.RegisterAdvertiserTextGameObject(advertiser.gameObject))
                {
                    advertiser.gameObject.SetActive(false);
                    Debug.LogError("Failed to register advertiser text game object");
                }
            }

            if (adchoice != null)
            {
                Texture2D texture = nativeAd.GetAdChoicesLogoTexture();
                if (texture != null)
                {
                    adchoice.texture = texture;
                }

                if (!nativeAd.RegisterAdChoicesLogoGameObject(adchoice.gameObject))
                {
                    Debug.LogError("Failed to register ad choices logo game object");
                }
            }
            ShowContent();
        }

        void OnDestroy()
        {
            if (NativeAdController.Instance != null)
            {
                NativeAdController.Instance.NativeAdViews.Remove(this);

                // Release the ad from this location
                if (!string.IsNullOrEmpty(location))
                {
                    NativeAdController.Instance.ReleaseAdFromLocation(location);
                }

                // Unsubscribe from events
                NativeAdController.Instance.OnAdLoaded -= OnAdLoadedHandler;
            }
        }

        public void SetObjectsActive(bool isActive)
        {
            Debug.Log("SetObjectsActive: " + isActive);
            foreach (var obj in hideObjects)
            {
                obj.SetActive(isActive);
            }
        }
    }
}
