using UnityEngine;
using DSDK.Logger;
using System;
using DSDK.Core;
using System.Threading;
using System.Threading.Tasks;

#if GDK_USE_FIREBASE
using Firebase.Extensions;
#endif

namespace DSDK.Mediation
{
    public abstract partial class BannerAdapterBase : AdTypeAdapterBase, IBannerAdapter
    {
        #region Properties

        protected BannerSettings BannerSettings => Settings as BannerSettings;
        public string BannerID => BannerSettings.Group;
        public string AdUnitId { get; protected set; }
        protected string AdNetwork = "";
        public bool IsAdaptive => BannerSettings.IsAdaptive;
        public bool IsCollapsible => BannerSettings.IsCollapsible;
        public override AdTypes Type => BannerSettings.IsCollapsible ? AdTypes.collapsible : AdTypes.banner;
        protected bool IsCallShowed = false;
        protected bool HasAdsToShow = false;
        protected bool HasRequestSuccess = false;
        protected CancellationTokenSource RefreshCancellation;
        public bool IsRefreshing { get; protected set; }
        public float RefreshInterval = 10;
        public Vector2 BannerSize { get; set; } = new Vector2(320f, 50f);
        public AdPositionSDK CurrentPosition { get; set; } = AdPositionSDK.None;
        public int CurrentOffsetX { get; set; } = 0;
        public int CurrentOffsetY { get; set; } = 0;
        public double Revenue { get; protected set; } = 0;
        public double FloorValue { get; set; } = 0;
        public string FloorCurrencyCode { get; set; } = "USD";
        protected Action<bool> OnLoadCallback;
        protected Action<bool> OnRefreshCallback;
        protected Action<bool> OnShowCallback;
        protected Action<bool> OnExpandCallback;
        protected Action<bool> OnHideCallback;
        protected Action OnShowedCallback;
        #endregion

        #region Constructor

        // Banner sẽ chỉ lấy duy nhất 1 banner Ads Unit thôi.
        protected BannerAdapterBase(AdTypesSetting settings, bool isTesting = false, int priority = 0) : base(settings, isTesting, priority)
        {
            AdUnitId = isTesting ? (string.IsNullOrEmpty(TestingID) ? Settings.Keys[0] : TestingID) : Settings.Keys[0];
            RefreshInterval = BannerSettings.RefreshTime;
#if UNITY_EDITOR
            EditorAdUnitId = $"{BannerID}_{BannerSettings.BannerType}";
#endif
        }

        #endregion

        #region Methods

        public override void Create()
        {
            CreateBannerView(BannerSettings.IsAdaptive, BannerSettings.IsCollapsible, BannerSettings.IsAdjustSizeForTablet, BannerSettings.IsBackgroundTransparent, BannerSettings.Position, CurrentOffsetX, CurrentOffsetY);
        }

        protected abstract void CreateBannerView(bool isAdaptive, bool isCollapsible, bool isAdjustSizeForTablet = true, bool isBackgroundTransparent = true, AdPositionSDK position = AdPositionSDK.None, int offsetX = 1000000, int offsetY = 1000000);

        public virtual bool IsShowed()
        {
            return IsCallShowed;
        }

        public virtual bool IsLoadSuccess()
        {
            return HasAdsToShow;
        }

        public virtual bool IsRequestSuccess()
        {
            return HasRequestSuccess;
        }

        // Chú ý Native Banner có override lại GetBannerPosition()
        public virtual AdPositionSDK GetBannerPosition()
        {
            return BannerSettings.Position;
        }

        public override void Load()
        {
            if (SkipLoad()) return;
            LoadInternal();
        }

        public virtual void Refresh()
        {
            if (SkipRefresh()) return;
            OnRefreshCallback?.Invoke(true);
            LoadInternal();
        }

        public virtual void SetAutoRefreshForMediation(bool value) { }
        protected abstract void LoadInternal();
        public abstract void Show(bool expand = false, AdPositionSDK position = AdPositionSDK.None, int offsetX = 1000000, int offsetY = 1000000);
        public abstract void Hide();

        protected virtual void StartRefresh()
        {
            DLogger.LogDebug($"[{GetType().Name}] Start auto refresh - {AdUnitId} - {RefreshInterval}", channel: "Mediation");
            IsRefreshing = true;
            RefreshCancellation = new CancellationTokenSource();
            AutoRefreshAsync(RefreshCancellation.Token); // Fire and forget
        }

        protected virtual void StopRefresh()
        {
            IsRefreshing = false;
            RefreshCancellation?.Cancel();
            RefreshCancellation?.Dispose();
            RefreshCancellation = null;
        }

        protected virtual async void AutoRefreshAsync(CancellationToken cancellationToken)
        {
            try
            {
                DLogger.LogDebug($"[{GetType().Name}] Count down auto refresh - {AdUnitId} - {RefreshInterval}", channel: "Mediation");
#if GDK_USE_FIREBASE
                await Task.Delay(TimeSpan.FromSeconds(RefreshInterval), cancellationToken).ContinueWithOnMainThread(t =>
                {
                    DLogger.LogDebug($"[{GetType().Name}] Time to refresh - {AdUnitId}", channel: "Mediation");
                    Refresh();
                });
#else
                await Task.Delay(TimeSpan.FromSeconds(RefreshInterval), cancellationToken);
                Refresh();
#endif
            }
            catch (Exception ex)
            {
                DLogger.LogError($"[{GetType().Name}] Error during auto refresh: {ex}", channel: "Mediation");
                OnRefreshCallback?.Invoke(false);
            }
        }

        public virtual void Collapse()
        {
        }

        public string GetBannerID()
        {
            return BannerID;
        }

        public MediationType GetNetworkType()
        {
            return NetworkType;
        }

        public void SetCallback(Action<bool> onLoad = null, Action<bool> onExpand = null, Action<bool> onRefresh = null, Action onShowed = null, Action<bool> onHide = null)
        {
            DLogger.LogDebug($"[BannerAdapter] Set callback: {BannerID} {onLoad != null} {onExpand != null} {onRefresh != null} {onShowed != null} {onHide != null}", channel: "Mediation");
            OnLoadCallback = onLoad;
            OnExpandCallback = onExpand;
            OnRefreshCallback = onRefresh;
            OnShowedCallback = onShowed;
            OnHideCallback = onHide;
        }

        protected virtual bool SkipLoad()
        {
            if (IsRequestingLoad)
            {
                return true;
            }

            if (HasAdsToShow)
            {
                return true;
            }

#if UNITY_EDITOR
            if (!HasAdsToShow)
            {
                Create();
            }

            return true;
#else
            return false;
#endif
        }

        // Skip Methods để lấy những điều kiện chung cho tất cả các mediation của banner
        protected virtual bool SkipHide()
        {
            StopRefresh();
#if UNITY_EDITOR
            if (IsCallShowed)
            {
                EditorAdsManager.Instance.HideAd(EditorAdUnitId, NetworkType, Type);
            }

            return true;
#else
            return false;
#endif
        }

        /// <summary>
        /// Kiểm tra, nếu mediation đó có collapsible banner thì ở phần skip show mới có thể truyền vào expand
        /// Ví dụ: Admob có collapsible banner thì ở skip show có thể truyền vào expand
        /// Ví dụ: MAX không có collapsible banner thì ở skip show không thể truyền vào expand
        /// </summary>
        /// <param name="expand"></param>
        /// <returns></returns>
        protected virtual bool SkipShow(bool expand = false)
        {
#if UNITY_EDITOR
            DLogger.LogDebug($"[{GetType().Name}] Show: {expand}", channel: "Mediation");
            OnCallbackShowRequest();
            EditorAdsManager.Instance.ShowAd(EditorAdUnitId, NetworkType, Type, null, expand);
            return true;
#else
            return false;
#endif
        }

        protected virtual bool SkipCreateBannerView(bool isAdaptive, bool isCollapsible)
        {
            IsRefreshing = false;
            RefreshInterval = BannerSettings.RefreshTime;
#if UNITY_EDITOR
            EditorAdsManager.Instance.CreateBannerAd(EditorAdUnitId, NetworkType, Type, BannerSettings.Position, isAdaptive);
            EditorAdsManager.Instance.SetCallback(idName: EditorAdUnitId, networkType: NetworkType, adTypes: Type,
                OnLoaded: OnCallbackLoadSuccess,
                OnLoadFailed: OnCallbackLoadFailure,
                OnShow: OnCallbackShowSuccess,
                OnShowFailed: OnCallbackShowFailure,
                OnClosed: OnCallbackClosed,
                OnExpanded: OnCallbackExpanded,
                OnCollapsed: OnCallbackCollapsed);
            return true;
#else
            return false;
#endif
        }

        protected virtual bool SkipDestroy()
        {
            StopRefresh();
#if UNITY_EDITOR
            EditorAdsManager.Instance.DestroyAd(EditorAdUnitId, NetworkType, Type);
            return true;
#else
            return false;
#endif
        }

        protected virtual bool SkipCollapse()
        {
#if UNITY_EDITOR
            if (BannerSettings.IsCollapsible)
            {
                EditorAdsManager.Instance.CollapseAd(EditorAdUnitId, NetworkType, Type);
            }

            return true;
#else
            return false;
#endif
        }

        protected virtual bool SkipRefresh()
        {
#if UNITY_EDITOR
            return true;
#else
            return false;
#endif
        }

        #endregion

        #region Callback

        protected override void OnCallbackRequestSuccess(int index, string demandSource)
        {
            HasRequestSuccess = true;
            IsRequestingLoad = false;
            HasAdsToShow = true;

            if (NetworkType == MediationType.costcenter_s2sbidding)
            {
                if (AdManager.Instance.IsEnableCostCenterS2sBidding)
                {
                    if (Type == AdTypes.banner)
                    {
                        BannerControl.Instance.DemandWinner = AdDemand.s2s;
                    }
                    else if (Type == AdTypes.mrec)
                    {
                        BannerControl.Instance.DemandWinnerMREC = AdDemand.s2s;
                    }
                }
            }

            DLogger.LogInfo($"[{GetType().Name}] Callback request success: {AdUnitId}, Winner: {demandSource}", channel: "Mediation");
        }

        protected override void OnCallbackRequestFailed(string errorCode, int index)
        {
            HasRequestSuccess = false;
            IsRequestingLoad = false;
            HasAdsToShow = false;

            if (NetworkType == MediationType.costcenter_s2sbidding)
            {
                if (AdManager.Instance.IsEnableCostCenterS2sBidding)
                {
                    if (Type == AdTypes.banner)
                    {
                        BannerControl.Instance.DemandWinner = AdDemand.mediation;
                    }
                    else if (Type == AdTypes.mrec)
                    {
                        BannerControl.Instance.DemandWinnerMREC = AdDemand.mediation;
                    }
                }
            }

            DLogger.LogInfo($"[{GetType().Name}] Callback request failed: {AdUnitId}, Error: {errorCode}", channel: "Mediation");
        }

        protected override void OnCallbackLoadRequest()
        {
            IsRequestingLoad = true;
            HasAdsToShow = false;
            AdNetwork = "";
            DLogger.LogDebug($"[{GetType().Name}] Ad load request", channel: "Mediation");
            TrackingManagerWrapper.Instance.TrackingAdEvent(mediation: NetworkType, adNetwork: AdNetwork, adFormat: Type, state: AdState.RequestLoad);
        }

        protected override void OnCallbackLoadSuccess(int index)
        {
            IsRequestingLoad = false;
            HasAdsToShow = true;

            if (OnLoadCallback != null)
            {
                DLogger.LogDebug($"[{GetType().Name}] Callback loaded successfully - {AdUnitId}", channel: "Mediation");

                OnLoadCallback(true);
            }

            DLogger.LogDebug($"[{GetType().Name}] Ad loaded successfully - {AdUnitId}", channel: "Mediation");
            TrackingManagerWrapper.Instance.TrackingAdEvent(mediation: NetworkType, adNetwork: AdNetwork, adFormat: Type, state: AdState.Loaded, loadTime: Metrics.LastLoadDuration);
            // Nếu đã gọi show, load được thì sẽ refresh
            if (BannerSettings.ManualRefresh && IsCallShowed)
            {
                StartRefresh();
            }

            if (NetworkType != MediationType.costcenter_s2sbidding)
            {
                if (AdManager.Instance.IsEnableCostCenterS2sBidding)
                {
                    if (Type == AdTypes.banner)
                    {
                        BannerControl.Instance.DemandWinner = AdDemand.mediation;

                        BannerControl.Instance.SetFloorValue(DSDK.Mediation.BannerID.default_banner_costcenter, Revenue * 1000);
                        BannerControl.Instance.Load(DSDK.Mediation.BannerID.default_banner_costcenter);

                    }
                    else if (Type == AdTypes.mrec)
                    {
                        BannerControl.Instance.DemandWinnerMREC = AdDemand.mediation;

                        BannerControl.Instance.SetFloorValue(DSDK.Mediation.BannerID.mrec_banner_costcenter, Revenue * 1000);
                        BannerControl.Instance.Load(DSDK.Mediation.BannerID.mrec_banner_costcenter);
                    }
                }
            }
        }

        protected override void OnCallbackLoadFailure(string errorCode, int index)
        {
            IsRequestingLoad = false;
            HasAdsToShow = false;
            HasRequestSuccess = false;

            if (OnLoadCallback != null)
            {
                DLogger.LogDebug($"[{GetType().Name}] Callback loaded failed - {AdUnitId}", channel: "Mediation");
                OnLoadCallback(false);
            }

            DLogger.LogError($"[{GetType().Name}] Failed to load ad: {errorCode} - {AdUnitId}", channel: "Mediation");
            TrackingManagerWrapper.Instance.TrackingAdEvent(mediation: NetworkType, adNetwork: AdNetwork, adFormat: Type, state: AdState.LoadFailed, errorCode: errorCode, loadTime: Metrics.LastLoadDuration);
            // Nếu đã gọi show, load fail thì sẽ refresh
            if (BannerSettings.ManualRefresh && IsCallShowed)
            {
                StartRefresh();
            }

            if (NetworkType != MediationType.costcenter_s2sbidding)
            {
                if (AdManager.Instance.IsEnableCostCenterS2sBidding)
                {
                    if (Type == AdTypes.banner)
                    {
                        BannerControl.Instance.SetFloorValue(DSDK.Mediation.BannerID.default_banner_costcenter, Revenue * 1000);
                        BannerControl.Instance.Load(DSDK.Mediation.BannerID.default_banner_costcenter);
                    }
                    else if (Type == AdTypes.mrec)
                    {
                        BannerControl.Instance.SetFloorValue(DSDK.Mediation.BannerID.mrec_banner_costcenter, Revenue * 1000);
                        BannerControl.Instance.Load(DSDK.Mediation.BannerID.mrec_banner_costcenter);
                    }
                }
            }
        }

        protected override void OnCallbackDestroy()
        {
            HasAdsToShow = false;
            HasRequestSuccess = false;

            if (NetworkType == MediationType.costcenter_s2sbidding)
            {
                if (AdManager.Instance.IsEnableCostCenterS2sBidding)
                {
                    if (Type == AdTypes.banner)
                    {
                        BannerControl.Instance.DemandWinner = AdDemand.mediation;
                    }
                    else if (Type == AdTypes.mrec)
                    {
                        BannerControl.Instance.DemandWinnerMREC = AdDemand.mediation;
                    }
                }
            }
        }

        protected override void OnCallbackShowRequest()
        {
            IsRequestingShow = true;
            IsCallShowed = true;
            DLogger.LogDebug($"[{GetType().Name}] Ad show request - {AdUnitId}", channel: "Mediation");
            TrackingManagerWrapper.Instance.TrackingAdEvent(mediation: NetworkType, adNetwork: AdNetwork, adFormat: Type, state: AdState.RequestShow);
        }

        protected override void OnCallbackShowing()
        {
            DLogger.LogDebug($"[{GetType().Name}] Ad showing - {AdUnitId}", channel: "Mediation");
        }

        protected override void OnCallbackShowSuccess()
        {
            IsRequestingShow = false;
            OnShowCallback?.Invoke(true);
            OnShowedCallback?.Invoke();
            DLogger.LogDebug($"[{GetType().Name}] Ad showed successfully - {AdUnitId}", channel: "Mediation");
            TrackingManagerWrapper.Instance.TrackingAdEvent(mediation: NetworkType, adNetwork: AdNetwork, adFormat: Type, state: AdState.ShowSuccess, showTime: Metrics.LastShowDuration);
            if (!IsCallShowed)
            {
                Hide();
            }
            else
            {
                // Nếu đã gọi show, đang show được thì sẽ refresh
                if (BannerSettings.ManualRefresh)
                {
                    StartRefresh();
                }
            }
        }

        protected override void OnCallbackShowFailure(string errorCode)
        {
            IsRequestingShow = false;
            OnShowCallback?.Invoke(false);
            DLogger.LogError($"[{GetType().Name}] Failed to show ad: {errorCode} - {AdUnitId}", channel: "Mediation");
            TrackingManagerWrapper.Instance.TrackingAdEvent(mediation: NetworkType, adNetwork: AdNetwork, adFormat: Type, state: AdState.ShowFailed, errorCode: errorCode, showTime: Metrics.LastShowDuration);
        }

        protected override void OnCallbackClosed()
        {
            IsRequestingShow = false;
            HasRequestSuccess = false;

            if (NetworkType == MediationType.costcenter_s2sbidding)
            {
                if (AdManager.Instance.IsEnableCostCenterS2sBidding)
                {
                    IsRequestingLoad = false;
                    HasAdsToShow = false;
                }
            }
            OnHideCallback?.Invoke(true);
            DLogger.LogDebug($"[{GetType().Name}] Ad closed - {AdUnitId}", channel: "Mediation");

            if (NetworkType == MediationType.costcenter_s2sbidding)
            {
                if (AdManager.Instance.IsEnableCostCenterS2sBidding)
                {
                    if (Type == AdTypes.banner)
                    {
                        BannerControl.Instance.DemandWinner = AdDemand.mediation;
                    }
                    else if (Type == AdTypes.mrec)
                    {
                        BannerControl.Instance.DemandWinnerMREC = AdDemand.mediation;
                    }
                }
            }

            // Nếu hide đi, thì sẽ stop refresh
            if (BannerSettings.ManualRefresh)
            {
                StopRefresh();
            }

            TrackingManagerWrapper.Instance.TrackingAdEvent(mediation: NetworkType, adNetwork: AdNetwork, adFormat: Type, state: AdState.Close);
        }

        protected override void OnCallbackClicked()
        {
            DLogger.LogDebug($"[{GetType().Name}] Ad clicked - {AdUnitId}", channel: "Mediation");
            TrackingManagerWrapper.Instance.TrackingAdEvent(mediation: NetworkType, adNetwork: AdNetwork, adFormat: Type, state: AdState.Click);
        }

        protected virtual void OnCallbackCollapsed()
        {
            OnExpandCallback?.Invoke(false);
            DLogger.LogDebug($"[{GetType().Name}] Ad collapsed - {AdUnitId}", channel: "Mediation");
            TrackingManagerWrapper.Instance.TrackingAdEvent(mediation: NetworkType, adNetwork: AdNetwork, adFormat: Type, state: AdState.Collapsed);
        }

        protected virtual void OnCallbackExpanded()
        {
            OnExpandCallback?.Invoke(true);
            DLogger.LogDebug($"[{GetType().Name}] Ad expanded - {AdUnitId}", channel: "Mediation");
            TrackingManagerWrapper.Instance.TrackingAdEvent(mediation: NetworkType, adNetwork: AdNetwork, adFormat: Type, state: AdState.Expanded);
        }
        #endregion
    }
}