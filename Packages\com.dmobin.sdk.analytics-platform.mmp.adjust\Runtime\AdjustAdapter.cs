﻿/***********************************************************************************************************************
 * Version: 0.0.69
 ***********************************************************************************************************************/

using UnityEngine;
using System.Threading.Tasks;
using AdjustSdk;
using System.Collections.Generic;
using DSDK.Logger;
using DSDK.Core;
using DSDK.AnalyticsPlatform;
using UnityEngine.Scripting;

[assembly: AlwaysLinkAssembly]
namespace DSDK.Analytics
{
    [Preserve]
    public class AdjustAdapter : MmpAdapter
    {
        AdjustSettings _adjustSettings;
        private TaskCompletionSource<bool> _initializationTcs;

        string AppToken
        {
            get
            {
                return _adjustSettings.AppToken;
            }
        }

        [Preserve]
        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSceneLoad)]
        public static void Initialize()
        {
            DLogger.LogInfo("[DSDK] Register AdjustAdapter", channel: "AnalyticsPlatform");
            TrackingManager.I.AddMmpAdapter(new AdjustAdapter());
        }

        public AdjustAdapter()
        {
            _initializationTcs = new TaskCompletionSource<bool>();
        }

        public sealed override void Init(AnalyticsConfigSDK config = null)
        {
            try
            {
                base.Init(config);
                DLogger.LogDebug($"[AdjustAdapter] Initializing AdjustAdapter", channel: "AnalyticsPlatform");
                var adjust = Resources.Load<Adjust>("Adjust");
                if (adjust == null)
                {
                    DLogger.LogError($"[AdjustAdapter] No Adjust object found. Please add Adjust object in Resources folder", channel: "AnalyticsPlatform");
                    _initializationTcs.SetResult(false);
                    return;
                }

                if (!adjust.startManually)
                {
                    _initializationTcs.SetResult(true);
                    return;
                }

                if (config == null)
                {
                    _initializationTcs.SetResult(false);
                    return;
                }

                _adjustSettings = config.adjustSettings;

                DLogger.LogWarning($"[AdjustAdapter] Adjust starting manually", channel: "AnalyticsPlatform");
                var appToken = !string.IsNullOrEmpty(AppToken) ? AppToken : adjust.appToken;

                if (string.IsNullOrEmpty(appToken))
                {
                    DLogger.LogError($"[AdjustAdapter] Adjust appToken is empty", channel: "AnalyticsPlatform");
                    return;
                }
                else
                {
                    DLogger.LogInfo($"[AdjustAdapter] Adjust appToken: {appToken}", channel: "AnalyticsPlatform");
                }
                AdjustConfig adjustConfig = new AdjustConfig(appToken, adjust.environment, adjust.logLevel == AdjustLogLevel.Suppress);
                adjustConfig.LogLevel = adjust.logLevel;
                adjustConfig.IsSendingInBackgroundEnabled = adjust.sendInBackground;
                adjustConfig.IsDeferredDeeplinkOpeningEnabled = adjust.launchDeferredDeeplink;
                adjustConfig.DefaultTracker = adjust.defaultTracker;
                // TODO: URL strategy
                adjustConfig.IsCoppaComplianceEnabled = adjust.coppaCompliance;
                adjustConfig.IsCostDataInAttributionEnabled = adjust.costDataInAttribution;
                adjustConfig.IsPreinstallTrackingEnabled = adjust.preinstallTracking;
                adjustConfig.PreinstallFilePath = adjust.preinstallFilePath;
                adjustConfig.IsAdServicesEnabled = adjust.adServices;
                adjustConfig.IsIdfaReadingEnabled = adjust.idfaReading;
                adjustConfig.IsLinkMeEnabled = adjust.linkMe;
                adjustConfig.IsSkanAttributionEnabled = adjust.skanAttribution;
                adjustConfig.SessionSuccessDelegate = OnSessionSuccess;
                adjustConfig.AttributionChangedDelegate = SetAttribution;
                Adjust.InitSdk(adjustConfig);
                _initializationTcs.SetResult(true);
                _ = InitializeAdjust();
            }
            catch (System.Exception ex)
            {
                DLogger.LogError($"[AdjustAdapter] Error initializing Adjust: {ex}", channel: "AnalyticsPlatform");
                _initializationTcs.SetResult(false);
            }
        }
        private async Task InitializeAdjust()
        {
            bool initSuccess = await this.WaitForInitialization();
            if (!initSuccess)
            {
                DLogger.LogError($"[AdjustAdapter] Failed to initialize Adjust", channel: "AnalyticsPlatform");
                return;
            }

            AnalyticsPlatformInstance.TryGetInstanceId((instanceId, type) =>
            {
                if (type != EAnalyticsPlatform.Firebase) return;
                AdjustAdapterWrapper.FirebaseAppInstanceId = instanceId;
                DLogger.LogInfo($"[AdjustAdapter] FirebaseAppInstanceId: {AdjustAdapterWrapper.FirebaseAppInstanceId}", channel: "AnalyticsPlatform");
            });

        }
        public Task<bool> WaitForInitialization()
        {
            return _initializationTcs.Task;
        }
        private void OnSessionSuccess(AdjustSessionSuccess sessionSuccessData)
        {
            DLogger.LogInfo("[AdjustAdapter] OnSessionSuccess", channel: "AnalyticsPlatform");
            DLogger.LogInfo($"[AdjustAdapter] Adjust adid: {sessionSuccessData.Adid}", channel: "AnalyticsPlatform");
            TrackingManager.I.SetUserProperty("attribution_id", sessionSuccessData.Adid);
            Adjust.GetAttribution(SetAttribution);
        }
        private void SetAttribution(AdjustAttribution adjustAttribution)

        {
            DLogger.LogInfo($"[AdjustAdapter] SetAttribution", channel: "AnalyticsPlatform");
            TrackingManager.I.SetUserProperty("adjust_campaign", adjustAttribution.Campaign);
            TrackingManager.I.SetUserProperty("adjust_network", adjustAttribution.Network);
            TrackingManager.I.SetUserProperty("adjust_adgroup", adjustAttribution.Adgroup);
            TrackingManager.I.SetUserProperty("adjust_creative", adjustAttribution.Creative);
            TrackingManager.I.SetUserProperty("adjust_cost_type", adjustAttribution.CostType);
            TrackingManager.I.SetUserProperty("adjust_cost_currency", adjustAttribution.CostCurrency);
            TrackingManager.I.SetUserProperty("adjust_cost_amount", adjustAttribution.CostAmount.ToString());
            TrackingManager.I.SetUserProperty("adjust_tracker_token", adjustAttribution.TrackerToken);
            TrackingManager.I.SetUserProperty("adjust_tracker_name", adjustAttribution.TrackerName);
            TrackingManager.I.SetUserProperty("adjust_click_label", adjustAttribution.ClickLabel);

        }
        public override void LogEvent(string eventName)
        {
            AdjustEvent adjustEvent = new AdjustEvent(eventName);
            Adjust.TrackEvent(adjustEvent);
        }

        public override void LogEvent(string eventName, IDictionary<string, object> parameters)
        {
            AdjustEvent adjustEvent = new AdjustEvent(eventName);
            foreach (KeyValuePair<string, object> parameter in parameters)
            {
                adjustEvent.AddPartnerParameter(parameter.Key, parameter.Value.ToString());
            }

            Adjust.TrackEvent(adjustEvent);
        }

        public override void LogEvent(string eventName, params (string, object)[] parameters)
        {
            DLogger.LogWarning("This function is not used for mmp", channel: "AnalyticsPlatform");
        }

        public override void SetUserProperty(string key, string value)
        {
            DLogger.LogWarning("This function is not used for mmp", channel: "AnalyticsPlatform");
        }

        public override void TrackIAPRevenue(double revenue, string currencyCode, string productId, string transactionId, string receipt)
        {
            if (string.IsNullOrEmpty(AppToken))
            {
                return;
            }
            AdjustEvent adjustEvent = new AdjustEvent(AppToken);
            adjustEvent.SetRevenue(revenue, currencyCode);
            adjustEvent.ProductId = productId;
            adjustEvent.AddPartnerParameter("revenue", revenue.ToString());
            adjustEvent.AddPartnerParameter("currency_code", currencyCode);
            adjustEvent.AddPartnerParameter("product_id", productId);
#if UNITY_IOS
            adjustEvent.TransactionId = transactionId;
            adjustEvent.AddPartnerParameter("transaction_id", transactionId);
            adjustEvent.AddPartnerParameter("receipt", receipt);
#endif
            Adjust.TrackEvent(adjustEvent);
        }
        public override void TrackAdRevenue(MediationType adMediation, string adFormat, double revenue, string adNetwork = "", string currency = "USD", string adUnit = "", string placement = "none")
        {
            AdjustAdRevenue adjustAdRevenue = new AdjustAdRevenue(GetSourceNameAdjust(adMediation));
            adjustAdRevenue.SetRevenue(revenue, currency);
            adjustAdRevenue.AdRevenueNetwork = adNetwork;
            adjustAdRevenue.AdRevenueUnit = adUnit;
            adjustAdRevenue.AdRevenuePlacement = placement;
            Adjust.TrackAdRevenue(adjustAdRevenue);

            if (adMediation == MediationType.costcenter_s2sbidding)
            {
                AdjustAdRevenue adjustAdRevenue2 = new AdjustAdRevenue(GetSourceNameAdjust(MediationType.adx));
                adjustAdRevenue2.SetRevenue(revenue, currency);
                adjustAdRevenue2.AdRevenueNetwork = adNetwork;
                adjustAdRevenue2.AdRevenueUnit = adUnit;
                adjustAdRevenue2.AdRevenuePlacement = placement;
                Adjust.TrackAdRevenue(adjustAdRevenue2);
            }
        }
        public static string GetSourceNameAdjust(MediationType adMediation)
        {
            switch (adMediation)
            {
                case MediationType.admob:
                    return "admob_sdk";
                case MediationType.ironSource:
                    return "ironsource_sdk";
                case MediationType.max:
                    return "applovin_max_sdk";
                case MediationType.pubscale:
                    return "admob_sdk";
                case MediationType.costcenter_s2sbidding:
                    return "ironsource_sdk";
                case MediationType.adx:
                    return "adx_sdk";
                default:
                    return "unknown";
            }
        }
    }
}