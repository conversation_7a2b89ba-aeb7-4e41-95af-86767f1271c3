using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading;
using GoogleMobileAds.Api;
using UnityEngine;

namespace DSDK.Mediation.GoogleMobileAds.Native
{
    public class NativeAdController : MonoBehaviour
    {
        public static NativeAdController Instance { get; private set; }

        private static SynchronizationContext _synchronizationContext;
        private static int _unityMainThreadId;

        // Pool of loaded native ads
        private Queue<NativeAd> _loadedAds = new Queue<NativeAd>();
        private Dictionary<string, NativeAd> _activeAds = new Dictionary<string, NativeAd>(); // location -> NativeAd

        private string[] _adUnitIds;
        private int _currentAdUnitIndex;

        // Events
        public Action<NativeAd> OnAdLoaded;
        public Action<string> OnAdFailedToLoad;
        public Action<AdValue, string> OnAdPaid; // AdValue, location
        public Action<string> OnAdShow; // location
        public Action<string> OnAdHide; // location

        public float RefreshRate = 20; // in seconds
        public int MaxRetries = 3;
        public int MaxAds = 3; // Maximum number of ads to keep loaded

        public List<NativeAdView> NativeAdViews = new List<NativeAdView>();

        private Coroutine _refreshCoroutine;
        private bool _isLoading;
        private int _loadingCount = 0;

        private void Awake()
        {
            Instance = this;
            SetUnityMainThreadSynchronizationContext();
        }

        private void OnDestroy()
        {
            if (_refreshCoroutine != null)
            {
                StopCoroutine(_refreshCoroutine);
            }

            // Destroy all loaded ads
            while (_loadedAds.Count > 0)
            {
                var ad = _loadedAds.Dequeue();
                ad?.Destroy();
            }

            // Destroy all active ads
            foreach (var kvp in _activeAds)
            {
                kvp.Value?.Destroy();
            }
            _activeAds.Clear();
        }

        public void Init(string[] adUnitIds)
        {
            _adUnitIds = adUnitIds;
            LoadAds(); // Load ads immediately after initialization
        }

        public void LoadAds()
        {
            int targetLoadCount = Mathf.Min(MaxAds, _adUnitIds.Length);
            int currentTotalAds = _loadedAds.Count + _activeAds.Count;
            int adsToLoad = targetLoadCount - currentTotalAds;

            for (int i = 0; i < adsToLoad; i++)
            {
                Load();
            }
        }

        private void Load(int retries = 0)
        {
            if (_loadingCount >= MaxAds)
            {
                return;
            }

            _loadingCount++;
            var adUnitId = GetAdUnitId();

            IEnumerator Retry()
            {
                retries++;
                yield return new WaitForSeconds((retries + 1) * 2);
                Load(retries);
            }

            void HandleAdFailedToLoad(object sender, AdFailedToLoadEventArgs args)
            {
                _loadingCount--;
                Debug.Log("Native ad failed to load: " + args.LoadAdError.GetMessage());
                if (retries < MaxRetries)
                {
                    StartCoroutine(Retry());
                    return;
                }
                RaiseAction(() => OnAdFailedToLoad?.Invoke(args.LoadAdError.GetMessage()));
            }

            void HandleNativeAdLoaded(object sender, NativeAdEventArgs args)
            {
                _loadingCount--;
                var nativeAd = args.nativeAd;
                nativeAd.OnPaidEvent += (sender, adValueArgs) => HandleAdPaid(adValueArgs, null);

                // Add to loaded ads pool
                _loadedAds.Enqueue(nativeAd);

                StartRefreshCoroutine(RefreshRate);
                RaiseAction(() => OnAdLoaded?.Invoke(nativeAd));
            }

            void HandleAdPaid(AdValueEventArgs args, string location)
            {
                RaiseAction(() => OnAdPaid?.Invoke(args.AdValue, location));
            }

            AdLoader adLoader = new AdLoader.Builder(adUnitId).ForNativeAd().Build();
            adLoader.OnNativeAdLoaded += HandleNativeAdLoaded;
            adLoader.OnAdFailedToLoad += HandleAdFailedToLoad;
            adLoader.LoadAd(new AdRequest());
        }

        internal static void RaiseAction(Action action)
        {
            if (action == null)
            {
                return;
            }

            if (_synchronizationContext == null ||
                Thread.CurrentThread.ManagedThreadId == _unityMainThreadId)
            {
                action();
                return;
            }

            _synchronizationContext.Post((state) => { action(); }, action);
        }

        internal static void SetUnityMainThreadSynchronizationContext()
        {
            _synchronizationContext = SynchronizationContext.Current;
            _unityMainThreadId = Thread.CurrentThread.ManagedThreadId;
        }

        private string GetAdUnitId()
        {
            var adUnitId = _adUnitIds[_currentAdUnitIndex];
            _currentAdUnitIndex++;
            if (_currentAdUnitIndex >= _adUnitIds.Length)
            {
                _currentAdUnitIndex = 0;
            }
            return adUnitId;
        }

        // Method to get an available ad for a specific location
        public NativeAd GetAdForLocation(string location)
        {
            // If already has an active ad for this location, return it
            if (_activeAds.ContainsKey(location))
            {
                return _activeAds[location];
            }

            // If no loaded ads available, return null
            if (_loadedAds.Count == 0)
            {
                return null;
            }

            // Get an ad from the pool and assign it to this location
            var ad = _loadedAds.Dequeue();
            _activeAds[location] = ad;

            // Update the OnPaidEvent to include location
            ad.OnPaidEvent += (sender, args) => HandleAdPaidForLocation(args, location);

            // Load a new ad to replace the one we just used
            LoadAds();

            return ad;
        }

        // Method to release an ad from a location
        public void ReleaseAdFromLocation(string location)
        {
            if (_activeAds.ContainsKey(location))
            {
                var ad = _activeAds[location];
                _activeAds.Remove(location);

                // Optionally destroy the ad or return it to pool
                ad?.Destroy();
            }
        }

        private void HandleAdPaidForLocation(AdValueEventArgs args, string location)
        {
            RaiseAction(() => OnAdPaid?.Invoke(args.AdValue, location));
        }

        // Method to trigger OnAdShow event
        public void TriggerAdShow(string location)
        {
            RaiseAction(() => OnAdShow?.Invoke(location));
        }

        // Method to trigger OnAdHide event
        public void TriggerAdHide(string location)
        {
            RaiseAction(() => OnAdHide?.Invoke(location));
        }

        public void ShowAllNativeAdViews()
        {
            foreach (var nativeAdView in NativeAdViews)
            {
                nativeAdView.ShowContent();
            }
        }

        public void HideAllNativeAdViews()
        {
            foreach (var nativeAdView in NativeAdViews)
            {
                nativeAdView.HideContent();
            }
        }

        public void HideAllNativeAdViews(NativeAdOverlayObject nativeAdOverlayObject)
        {
            foreach (var nativeAdView in NativeAdViews)
            {
                nativeAdView.HideContent(nativeAdOverlayObject);
            }
        }

        private void StartRefreshCoroutine(float delayInSeconds)
        {
            // Dừng coroutine cũ nếu nó đang chạy để bắt đầu một chu trình mới
            if (_refreshCoroutine != null)
            {
                StopCoroutine(_refreshCoroutine);
            }
            _refreshCoroutine = StartCoroutine(RefreshAd(delayInSeconds));
        }

        private IEnumerator RefreshAd(float delay)
        {
            Debug.Log($"Scheduling next ad request in {delay} seconds.");
            yield return new WaitForSeconds(delay);
            LoadAds(); // Load new ads to maintain the pool
        }
    }
}
