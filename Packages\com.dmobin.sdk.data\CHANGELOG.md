# Changelog

All notable changes to this package will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](http://semver.org/spec/v2.0.0.html).

## [1.0.9] - 2025-08-01

### English

- Add automatic code generation for DataMonitor.

### Tiếng Việt

- Thêm tự động generate code cho DataMonitor

## [1.0.8] - 2025-07-30

### English

- Fix bug when saving data using DataSaveType.PlayerPrefs.

### Tiếng Việt

- Sửa lỗi lưu data khi sử dụng DataSaveType.PlayerPrefs

## [1.0.7] - 2025-07-24

### English

- Fix bug where values were not updated in the Inspector on first data load.

### Tiếng Việt

- Sửa lỗi không cập nhật giá trị trên Inspector l<PERSON><PERSON> đầu load data.

## [1.0.6] - 2025-07-24

### English

- Fix bug where values were not updated in the Inspector on first data load.

### Tiếng Việt

- Sửa lỗi không cập nhật giá trị trên Inspector lần đầu load data.

## [1.0.5] - 2025-07-24

### English

- Fix bug causing duplicate data when loading data of type List.

### Tiếng Việt

- Sửa lỗi trùng lặp dữ liệu khi tải dữ liệu dạng List.

## [1.0.4] - 2025-07-10

### English

- Remove `isRemoveAd` data field from `GameData` class.

### Tiếng Việt

- Xoá trường dữ liệu `isRemoveAd` trong class `GameData`.

## [1.0.3] - 2025-07-02

### English

- Fix typo in script name from FileDataHanlder to FileDataHandler

### Tiếng Việt

- Sửa lỗi chính tả tên script từ FileDataHanlder thành FileDataHandler

## [1.0.2] - 2025-06-30

### English

- Add RefreshAllInstances in GetInstance of DataMonitors

### Tiếng Việt

- Thêm RefreshAllInstances trong GetInstance của DataMonitors

## [1.0.1] - 2025-06-28

### English

- Add exception handling for data processing
- Load, save and delete data can be used in editor

### Tiếng Việt

- Thêm ngoại lệ khi xử lý dữ liệu
- Tải, lưu và xoá dữ liệu có thể sử dụng trên editor

## [1.0.0] - 2025-04-12

### English

- First release
- Add basic data management classes
- Support for storing and loading data from PlayerPrefs
- Add basic data encryption and decryption methods
- Support for storing data in JSON format
- Add data conversion utilities
- Use Unity package with name "data.extend.unitypackage"

### Tiếng Việt

- Phát hành lần đầu
- Thêm các lớp quản lý dữ liệu cơ bản
- Hỗ trợ lưu trữ và tải dữ liệu từ PlayerPrefs
- Thêm các phương thức mã hóa và giải mã dữ liệu cơ bản
- Hỗ trợ lưu trữ dữ liệu dưới dạng JSON
- Thêm các tiện ích chuyển đổi dữ liệu
- Sử dụng Unity package với tên "data.extend.unitypackage"
