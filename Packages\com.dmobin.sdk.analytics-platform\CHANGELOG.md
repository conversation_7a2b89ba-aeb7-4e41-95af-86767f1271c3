# Changelog

All notable changes to this package will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](http://semver.org/spec/v2.0.0.html).

## [1.1.5] - 2025-08-01

### English

#### Changed

- Fixed the editor to automatically generate code for RemoteConfigMonitor.

#### Dependencies

- Updated dependencies:
  - com.dmobin.sdk.core: 1.1.7

### Tiếng Việt

#### Thay đổi

- Sửa editor để tự động sinh code cho RemoteConfigMonitor.

#### Cập nhật phụ thuộc

- Cập nhật dependencies:
  - com.dmobin.sdk.core: 1.1.7

## [1.1.4] - 2025-07-25

### English

#### Dependencies

- Updated dependencies:
  - com.dmobin.sdk.core: 1.1.5

### Tiếng Việt

#### Cậ<PERSON> nhật phụ thuộc

- Cập nhật dependencies:
  - com.dmobin.sdk.core: 1.1.5

## [1.1.3] - 2025-07-25

### English

#### Changed

- Modified the logic to remove the condition for tracking `ad_impression` events when Firebase Link Admob is used with mediation types other than Google Mobile Ad.

#### Dependencies

- Updated dependencies:
  - com.dmobin.sdk.core: 1.1.4

### Tiếng Việt

#### Thay đổi

- Chỉnh sửa loại bỏ điều kiện track event `ad_impression` khi Firebase Link Admob đối với các loại mediation không phải Google Mobile Ad.

#### Cập nhật phụ thuộc

- Cập nhật dependencies:
  - com.dmobin.sdk.core: 1.1.4

## [1.1.2] - 2025-07-23

### English

#### Changed

- Updated Unity package.

### Tiếng Việt

#### Thay đổi

- Cập nhật Unity package.

## [1.1.1] - 2025-07-23

### English

#### Added

- Added new ad location properties: `BannerLocation`, `InterstitialLocation`, `RewardedLocation`, `NativeLocation`, `MrecLocation`, `AppOpenLocation`, `NativeFullscreenLocation`, `AudioAdLocation`, `IapLocation` in `TrackingManager` to allow more flexible ad event tracking.
- Added method `GetLocationWithAdFormat(AdTypes adFormat)` to return the correct ad location based on ad format.
- Added new tracking methods for ad impression and revenue events:
  - `TrackingAdImpressionNativeBannerCostCenter`
  - `TrackingAdImpressionNativeAdmobCostCenter`
  - `TrackingAdImpressionCostCenterS2sBidding`
- Added new enum value `AdImpressionCostCenterType.All` for more comprehensive ad impression tracking.
- Added logic to handle test devices for analytics events.

#### Changed

- Refactored ad event tracking logic to use new location properties and methods.
- Improved handling of user properties and event normalization.
- Updated cost center tracking logic to support new ad formats and mediation types.

#### Dependencies

- Updated dependencies:
  - com.dmobin.sdk.core: 1.1.2

### Tiếng Việt

#### Thêm mới

- Thêm các thuộc tính vị trí quảng cáo mới: `BannerLocation`, `InterstitialLocation`, `RewardedLocation`, `NativeLocation`, `MrecLocation`, `AppOpenLocation`, `NativeFullscreenLocation`, `AudioAdLocation`, `IapLocation` trong `TrackingManager` để hỗ trợ tracking sự kiện quảng cáo linh hoạt hơn.
- Thêm phương thức `GetLocationWithAdFormat(AdTypes adFormat)` để trả về vị trí quảng cáo phù hợp theo loại quảng cáo.
- Thêm các hàm tracking mới cho sự kiện impression và revenue quảng cáo:
  - `TrackingAdImpressionNativeBannerCostCenter`
  - `TrackingAdImpressionNativeAdmobCostCenter`
  - `TrackingAdImpressionCostCenterS2sBidding`
- Thêm giá trị enum mới `AdImpressionCostCenterType.All` để tracking impression quảng cáo toàn diện hơn.
- Thêm logic xử lý thiết bị test cho các sự kiện analytics.

#### Thay đổi

- Refactor lại logic tracking sự kiện quảng cáo để sử dụng các thuộc tính vị trí mới và phương thức mới.
- Cải thiện xử lý user properties và chuẩn hóa tên sự kiện.
- Cập nhật logic tracking cost center để hỗ trợ các loại quảng cáo và mediation mới.

#### Dependencies

- Cập nhật dependencies:
  - com.dmobin.sdk.core: 1.1.2

## [1.1.0] - 2025-07-16

### English

#### Added

- Added tracking event: TrackingAdImpressionCostCenterS2sBidding

#### Changed

- Updated dependencies:
  - com.dmobin.sdk.core: 1.1.0
  - com.dmobin.sdk.utils: 1.0.7

### Tiếng Việt

#### Thêm mới

- Thêm tracking event: TrackingAdImpressionCostCenterS2sBidding

#### Thay đổi

- Cập nhật dependencies:
  - com.dmobin.sdk.core: 1.1.0
  - com.dmobin.sdk.utils: 1.0.7

## [1.0.2] - 2025-07-06

### English

#### Changed

- Fixed remote config loading logic

### Tiếng Việt

#### Thay đổi

- Sửa logic load từ remote config

## [1.0.1] - 2025-06-19

### English

#### Added

- Added enum tracking case: AdRevenueSdkCostCenterFollowZeroRevenue
- Added default AdRevenueSdkCostCenterFollowZeroRevenue in AnalyticsConfigSDK to handle ad_revenue_sdk event following zero revenue value cases

### Tiếng Việt

#### Thêm mới

- Thêm enum tracking case: AdRevenueSdkCostCenterFollowZeroRevenue
- Thêm mặc định AdRevenueSdkCostCenterFollowZeroRevenue trong AnalyticsConfigSDK xử lý cho event ad_revenue_sdk follow theo trường hợp value revenue bằng 0

## [1.0.0] - 2025-04-11

### English

#### Added

- First release.
- Initial stable release of the Analytics Platform package
- Unified analytics API across multiple platforms
- Remote configuration system with caching and fallback
- Support for Firebase, AppMetrica, and Adjust platforms
- Adapter pattern for seamless integration with different analytics providers
- Event tracking and user property management
- Automatic initialization and internet connectivity handling
- Debug configuration with testing ID support

#### Changed

- Refactored the core analytics architecture for better extensibility
- Improved error handling and logging
- Enhanced performance for remote configuration fetching

#### Fixed

- None (initial release).

### Tiếng Việt

#### Thêm mới

- Bản release đầu tiên.
- Phát hành phiên bản ổn định đầu tiên của gói Analytics Platform
- API phân tích thống nhất trên nhiều nền tảng
- Hệ thống cấu hình từ xa với bộ nhớ đệm và dự phòng
- Hỗ trợ cho các nền tảng Firebase, AppMetrica và Adjust
- Mẫu adapter để tích hợp liền mạch với các nhà cung cấp phân tích khác nhau
- Theo dõi sự kiện và quản lý thuộc tính người dùng
- Tự động khởi tạo và xử lý kết nối internet
- Cấu hình gỡ lỗi với hỗ trợ ID kiểm tra

#### Thay đổi

- Cấu trúc lại kiến trúc phân tích cốt lõi để mở rộng tốt hơn
- Cải thiện xử lý lỗi và ghi nhật ký
- Nâng cao hiệu suất cho việc tìm nạp cấu hình từ xa

#### Sửa lỗi

- Không có (bản phát hành đầu tiên).
