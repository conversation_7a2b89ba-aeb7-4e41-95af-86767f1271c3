{"dependencies": {"com.applovin.mediation.ads": {"version": "file:com.applovin.mediation.ads", "depth": 0, "source": "embedded", "dependencies": {"com.google.external-dependency-manager": "1.2.185"}}, "com.boxqkrtm.ide.cursor": {"version": "2.0.25", "depth": 0, "source": "registry", "dependencies": {"com.unity.test-framework": "1.1.9"}, "url": "https://package.openupm.com"}, "com.dmobin.adjust": {"version": "file:com.dmobin.adjust", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.costcenter.mediation.s2sbidding": {"version": "file:com.dmobin.costcenter.mediation.s2sbidding", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.esotericsoftware.spine2d": {"version": "file:com.dmobin.esotericsoftware.spine2d", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.esotericsoftware.spine2d-timeline": {"version": "file:com.dmobin.esotericsoftware.spine2d-timeline", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.facebook": {"version": "file:com.dmobin.facebook", "depth": 0, "source": "embedded", "dependencies": {"com.google.external-dependency-manager": "1.2.185"}}, "com.dmobin.game.manager": {"version": "file:com.dmobin.game.manager", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.sdk.core": "1.1.1", "com.dmobin.sdk.utils": "1.0.7", "com.dmobin.sdk.analytics-platform": "1.1.0", "com.dmobin.sdk.data": "1.0.4"}}, "com.dmobin.google.ads.mobile": {"version": "file:com.dmobin.google.ads.mobile", "depth": 0, "source": "embedded", "dependencies": {"com.google.external-dependency-manager": "1.2.185", "com.unity.modules.androidjni": "1.0.0"}}, "com.dmobin.google.firebase.analytics": {"version": "file:com.dmobin.google.firebase.analytics", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.google.firebase.app": {"version": "file:com.dmobin.google.firebase.app", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.google.firebase.crashlytics": {"version": "file:com.dmobin.google.firebase.crashlytics", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.google.firebase.messaging": {"version": "file:com.dmobin.google.firebase.messaging", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.google.firebase.remote-config": {"version": "file:com.dmobin.google.firebase.remote-config", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.levelplay": {"version": "file:com.dmobin.levelplay", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.levelplay.adquality": "7.23.2"}}, "com.dmobin.levelplay.adquality": {"version": "file:com.dmobin.levelplay.adquality", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.luispedrofonseca.procamera2d": {"version": "file:com.dmobin.luispedrofonseca.procamera2d", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.pubscale.tool": {"version": "file:com.dmobin.pubscale.tool", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.sdk.utils": "1.0.0"}}, "com.dmobin.sdk.analytics-platform": {"version": "file:com.dmobin.sdk.analytics-platform", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.sdk.core": "1.1.7", "com.dmobin.sdk.utils": "1.0.7"}}, "com.dmobin.sdk.analytics-platform.appmetrica": {"version": "file:com.dmobin.sdk.analytics-platform.appmetrica", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.sdk.analytics-platform.firebase": {"version": "file:com.dmobin.sdk.analytics-platform.firebase", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.google.firebase.app": "12.5.0", "com.google.external-dependency-manager": "1.2.185"}}, "com.dmobin.sdk.analytics-platform.firebase.analytics": {"version": "file:com.dmobin.sdk.analytics-platform.firebase.analytics", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.google.firebase.app": "12.5.0", "com.dmobin.google.firebase.analytics": "12.5.0", "com.google.external-dependency-manager": "1.2.185"}}, "com.dmobin.sdk.analytics-platform.firebase.crashlytics": {"version": "file:com.dmobin.sdk.analytics-platform.firebase.crashlytics", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.google.firebase.app": "12.5.0", "com.dmobin.google.firebase.crashlytics": "12.5.0", "com.google.external-dependency-manager": "1.2.185"}}, "com.dmobin.sdk.analytics-platform.firebase.messaging": {"version": "file:com.dmobin.sdk.analytics-platform.firebase.messaging", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.google.firebase.app": "12.5.0", "com.dmobin.google.firebase.messaging": "12.5.0", "com.google.external-dependency-manager": "1.2.185", "com.unity.mobile.notifications": "2.3.2"}}, "com.dmobin.sdk.analytics-platform.firebase.remote-config": {"version": "file:com.dmobin.sdk.analytics-platform.firebase.remote-config", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.sdk.analytics-platform": "1.0.0", "com.dmobin.google.firebase.app": "12.5.0", "com.dmobin.google.firebase.remote-config": "12.5.0", "com.google.external-dependency-manager": "1.2.185"}}, "com.dmobin.sdk.analytics-platform.mmp.adjust": {"version": "file:com.dmobin.sdk.analytics-platform.mmp.adjust", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.adjust": "5.1.3"}}, "com.dmobin.sdk.animation-sequencer-dotween": {"version": "file:com.dmobin.sdk.animation-sequencer-dotween", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.sdk.audio": {"version": "file:com.dmobin.sdk.audio", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.sdk.core": "1.1.2", "com.dmobin.sdk.data": "1.0.4"}}, "com.dmobin.sdk.camera": {"version": "file:com.dmobin.sdk.camera", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.sdk.core": "1.0.5"}}, "com.dmobin.sdk.core": {"version": "file:com.dmobin.sdk.core", "depth": 0, "source": "embedded", "dependencies": {"com.unity.nuget.newtonsoft-json": "3.2.1", "com.dmobin.sdk.utils": "1.0.7", "com.dmobin.sdk.scenesystem": "1.0.4", "com.dmobin.game.manager": "1.0.5"}}, "com.dmobin.sdk.data": {"version": "file:com.dmobin.sdk.data", "depth": 0, "source": "embedded", "dependencies": {"com.unity.nuget.newtonsoft-json": "3.2.1"}}, "com.dmobin.sdk.facebook": {"version": "file:com.dmobin.sdk.facebook", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.facebook": "18.0.0"}}, "com.dmobin.sdk.forceupdate": {"version": "file:com.dmobin.sdk.forceupdate", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.sdk.i2-localization": {"version": "file:com.dmobin.sdk.i2-localization", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.sdk.inapppurchase": {"version": "file:com.dmobin.sdk.inapppurchase", "depth": 0, "source": "embedded", "dependencies": {"com.unity.purchasing": "4.13.0", "com.unity.nuget.newtonsoft-json": "3.2.1"}}, "com.dmobin.sdk.internetchecker": {"version": "file:com.dmobin.sdk.internetchecker", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.sdk.mediation": {"version": "file:com.dmobin.sdk.mediation", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.sdk.core": "1.1.7", "com.unity.ads.ios-support": "1.0.0"}}, "com.dmobin.sdk.mediation.admob": {"version": "file:com.dmobin.sdk.mediation.admob", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.sdk.mediation": "1.0.18", "com.dmobin.google.ads.mobile": "9.5.0"}}, "com.dmobin.sdk.mediation.costcenter-s2sbidding": {"version": "file:com.dmobin.sdk.mediation.costcenter-s2sbidding", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.sdk.mediation": "1.1.18", "com.dmobin.costcenter.mediation.s2sbidding": "1.7.0"}}, "com.dmobin.sdk.mediation.googlemobileads-native": {"version": "file:com.dmobin.sdk.mediation.googlemobileads-native", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.sdk.mediation.levelplay": {"version": "file:com.dmobin.sdk.mediation.levelplay", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.sdk.mediation": "1.1.24", "com.dmobin.levelplay": "8.6.0"}}, "com.dmobin.sdk.mediation.max": {"version": "file:com.dmobin.sdk.mediation.max", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.sdk.mediation": "1.1.22", "com.applovin.mediation.ads": "8.0.1"}}, "com.dmobin.sdk.mediation.yandex": {"version": "file:com.dmobin.sdk.mediation.yandex", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.sdk.mediation": "1.0.4", "com.dmobin.yandexmobileads.lite": "7.8.0"}}, "com.dmobin.sdk.mobile-notification": {"version": "file:com.dmobin.sdk.mobile-notification", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.sdk.core": "1.0.0", "com.unity.mobile.notifications": "2.3.2"}}, "com.dmobin.sdk.mobileinput": {"version": "file:com.dmobin.sdk.mobileinput", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.sdk.native-share": {"version": "file:com.dmobin.sdk.native-share", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.sdk.nativeads": {"version": "file:com.dmobin.sdk.nativeads", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.sdk.particle-system-preview": {"version": "file:com.dmobin.sdk.particle-system-preview", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.sdk.pooling": {"version": "file:com.dmobin.sdk.pooling", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.sdk.privacy": {"version": "file:com.dmobin.sdk.privacy", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.sdk.core": "1.1.6", "com.unity.ads.ios-support": "1.0.0"}}, "com.dmobin.sdk.rating": {"version": "file:com.dmobin.sdk.rating", "depth": 0, "source": "embedded", "dependencies": {"com.google.play.review": "1.8.4"}}, "com.dmobin.sdk.scenesystem": {"version": "file:com.dmobin.sdk.scenesystem", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.sdk.spine2d": {"version": "file:com.dmobin.sdk.spine2d", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.esotericsoftware.spine2d": "4.1.0", "com.dmobin.sdk.core": "1.0.4"}}, "com.dmobin.sdk.uiparticle": {"version": "file:com.dmobin.sdk.uiparticle", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.sdk.uisystem": {"version": "file:com.dmobin.sdk.uisystem", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.sdk.core": "1.1.6", "com.dmobin.sdk.uiparticle": "1.0.1"}}, "com.dmobin.sdk.unity-packages": {"version": "file:com.dmobin.sdk.unity-packages", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.sdk.utils": {"version": "file:com.dmobin.sdk.utils", "depth": 0, "source": "embedded", "dependencies": {}}, "com.dmobin.sdk.vibration": {"version": "file:com.dmobin.sdk.vibration", "depth": 0, "source": "embedded", "dependencies": {"com.dmobin.sdk.data": "1.0.1"}}, "com.dmobin.yandexmobileads.lite": {"version": "file:com.dmobin.yandexmobileads.lite", "depth": 0, "source": "embedded", "dependencies": {}}, "com.google.external-dependency-manager": {"version": "file:com.google.external-dependency-manager", "depth": 0, "source": "embedded", "dependencies": {}}, "com.google.play.common": {"version": "1.9.2", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://package.openupm.com"}, "com.google.play.core": {"version": "1.8.6", "depth": 1, "source": "registry", "dependencies": {"com.google.external-dependency-manager": "1.2.185"}, "url": "https://package.openupm.com"}, "com.google.play.review": {"version": "1.8.4", "depth": 0, "source": "registry", "dependencies": {"com.google.play.common": "1.9.2", "com.google.play.core": "1.8.6"}, "url": "https://package.openupm.com"}, "com.unity.2d.animation": {"version": "9.1.0", "depth": 1, "source": "registry", "dependencies": {"com.unity.2d.common": "8.0.2", "com.unity.2d.sprite": "1.0.0", "com.unity.collections": "1.1.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.uielements": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.2d.aseprite": {"version": "1.1.1", "depth": 1, "source": "registry", "dependencies": {"com.unity.2d.common": "6.0.6", "com.unity.2d.sprite": "1.0.0", "com.unity.mathematics": "1.2.6", "com.unity.modules.animation": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.2d.common": {"version": "8.0.2", "depth": 2, "source": "registry", "dependencies": {"com.unity.burst": "1.7.3", "com.unity.2d.sprite": "1.0.0", "com.unity.mathematics": "1.1.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.uielements": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.2d.pixel-perfect": {"version": "5.0.3", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.2d.psdimporter": {"version": "8.0.4", "depth": 1, "source": "registry", "dependencies": {"com.unity.2d.common": "8.0.2", "com.unity.2d.sprite": "1.0.0", "com.unity.2d.animation": "9.1.0"}, "url": "https://packages.unity.com"}, "com.unity.2d.sprite": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {}}, "com.unity.2d.spriteshape": {"version": "9.0.2", "depth": 1, "source": "registry", "dependencies": {"com.unity.2d.common": "8.0.1", "com.unity.mathematics": "1.1.0", "com.unity.modules.physics2d": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.2d.tilemap": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {"com.unity.modules.tilemap": "1.0.0", "com.unity.modules.uielements": "1.0.0"}}, "com.unity.2d.tilemap.extras": {"version": "3.1.2", "depth": 1, "source": "registry", "dependencies": {"com.unity.ugui": "1.0.0", "com.unity.2d.tilemap": "1.0.0", "com.unity.modules.tilemap": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.ads.ios-support": {"version": "1.0.0", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.burst": {"version": "1.8.12", "depth": 3, "source": "registry", "dependencies": {"com.unity.mathematics": "1.2.1", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.collab-proxy": {"version": "2.5.1", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.collections": {"version": "1.2.4", "depth": 2, "source": "registry", "dependencies": {"com.unity.burst": "1.6.6", "com.unity.test-framework": "1.1.31"}, "url": "https://packages.unity.com"}, "com.unity.device-simulator.devices": {"version": "1.0.0", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.ext.nunit": {"version": "1.0.6", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.feature.2d": {"version": "2.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.2d.animation": "9.1.0", "com.unity.2d.pixel-perfect": "5.0.3", "com.unity.2d.psdimporter": "8.0.4", "com.unity.2d.sprite": "1.0.0", "com.unity.2d.spriteshape": "9.0.2", "com.unity.2d.tilemap": "1.0.0", "com.unity.2d.tilemap.extras": "3.1.2", "com.unity.2d.aseprite": "1.1.1"}}, "com.unity.ide.rider": {"version": "3.0.36", "depth": 0, "source": "registry", "dependencies": {"com.unity.ext.nunit": "1.0.6"}, "url": "https://packages.unity.com"}, "com.unity.ide.visualstudio": {"version": "2.0.23", "depth": 0, "source": "registry", "dependencies": {"com.unity.test-framework": "1.1.9"}, "url": "https://packages.unity.com"}, "com.unity.mathematics": {"version": "1.2.6", "depth": 2, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.mobile.notifications": {"version": "2.3.2", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.androidjni": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.nuget.newtonsoft-json": {"version": "3.2.1", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.purchasing": {"version": "4.13.0", "depth": 1, "source": "registry", "dependencies": {"com.unity.ugui": "1.0.0", "com.unity.services.core": "1.12.5", "com.unity.modules.androidjni": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.services.core": {"version": "1.12.5", "depth": 1, "source": "registry", "dependencies": {"com.unity.modules.androidjni": "1.0.0", "com.unity.nuget.newtonsoft-json": "3.2.1", "com.unity.modules.unitywebrequest": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.sysroot": {"version": "2.0.10", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.sysroot.linux-x86_64": {"version": "2.0.9", "depth": 1, "source": "registry", "dependencies": {"com.unity.sysroot": "2.0.10"}, "url": "https://packages.unity.com"}, "com.unity.test-framework": {"version": "1.1.33", "depth": 0, "source": "registry", "dependencies": {"com.unity.ext.nunit": "1.0.6", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.textmeshpro": {"version": "3.0.9", "depth": 0, "source": "registry", "dependencies": {"com.unity.ugui": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.timeline": {"version": "1.7.7", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.director": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.particlesystem": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.toolchain.win-x86_64-linux-x86_64": {"version": "2.0.10", "depth": 0, "source": "registry", "dependencies": {"com.unity.sysroot": "2.0.10", "com.unity.sysroot.linux-x86_64": "2.0.9"}, "url": "https://packages.unity.com"}, "com.unity.ugui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0"}}, "com.unity.modules.ai": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.androidjni": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.animation": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.assetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.audio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.cloth": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0"}}, "com.unity.modules.director": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.animation": "1.0.0"}}, "com.unity.modules.imageconversion": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.imgui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.jsonserialize": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.particlesystem": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics2d": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.screencapture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.subsystems": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {"com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.terrain": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.terrainphysics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0", "com.unity.modules.terrain": "1.0.0"}}, "com.unity.modules.tilemap": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics2d": "1.0.0"}}, "com.unity.modules.ui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.uielements": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.umbra": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unityanalytics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.unitywebrequest": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unitywebrequestassetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}}, "com.unity.modules.unitywebrequestaudio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.audio": "1.0.0"}}, "com.unity.modules.unitywebrequesttexture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.unitywebrequestwww": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestassetbundle": "1.0.0", "com.unity.modules.unitywebrequestaudio": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.vehicles": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0"}}, "com.unity.modules.video": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.ui": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}}, "com.unity.modules.vr": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.modules.xr": "1.0.0"}}, "com.unity.modules.wind": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.xr": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.subsystems": "1.0.0"}}}}