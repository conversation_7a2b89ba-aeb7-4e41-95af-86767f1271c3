using System.Collections;
using System.Collections.Generic;
using DSDK.Extensions.NativeAds;
using DSDK.Mediation.AdmobNative.Api;
using GoogleMobileAds.Api;
using UnityEngine;

public class CompatAdmobDemo : MonoBehaviour
{
    [SerializeField] private CompatAdsManager _compatAdsManager;

    private void Awake()
    {
        RequestConfiguration requestConfiguration = new RequestConfiguration();
        requestConfiguration.TestDeviceIds.Add("B8D3491548F2F3B2F313E8C9785E985F"); // Nokia 6.1 Plus
        MobileAds.SetRequestConfiguration(requestConfiguration);

        _compatAdsManager.SetNativeBannerIds(new string[] { "ca-app-pub-3940256099942544/2247696110", "ca-app-pub-3940256099942544/2247696110" });
        _compatAdsManager.UseNativeBannerMultiIds(false, false);
        _compatAdsManager.NativeBannerOptions.Position = NativeBanner.Position.Left;
        _compatAdsManager.NativeBannerOptions.IsCollapsible = true;
        _compatAdsManager.SetNativeBannerTimeInterval(5);
        _compatAdsManager.LoadNativeBanner(true);

        _compatAdsManager.SetFullScreenIds(new string[] { "ca-app-pub-3940256099942544/2247696110" });
    }

    public void ShowBanner()
    {
        _compatAdsManager.ShowNativeBanner();
    }

    public void ShowCollapsedBanner()
    {
        _compatAdsManager.ShowNativeBanner(false);
    }

    public void ShowExpandedBanner()
    {
        _compatAdsManager.ShowNativeBanner(true);
    }

    public void HideBanner()
    {
        _compatAdsManager.HideNativeBanner();
    }

    public void ExpandBanner()
    {
        _compatAdsManager.ExpandNativeBanner();
    }

    public void ShowFullscreen()
    {
        _compatAdsManager.ShowNativeFullScreen();
    }

    public void ShowRewarded()
    {
    }

    public void ShowNative()
    {
    }
}
