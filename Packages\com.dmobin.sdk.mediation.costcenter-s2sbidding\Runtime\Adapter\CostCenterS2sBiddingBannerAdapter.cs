using UnityEngine;
using System;
using DSDK.Core;
using DSDK.Logger;

namespace DSDK.Mediation
{
    public class CostCenterS2sBiddingBannerAdapter : BannerAdapterBase
    {
        public CostCenterS2sBiddingBannerAdapter(AdTypesSetting settings, bool isTesting, int priority) : base(settings, isTesting, priority)
        {
        }

        public override MediationType NetworkType => MediationType.costcenter_s2sbidding;

        private bool IsCreated = false;

        S2sSdkBase.BannerPosition GetBannerPosition(AdPositionSDK position)
        {
            switch (position)
            {
                case AdPositionSDK.TopLeft:
                    return S2sSdkBase.BannerPosition.TopLeft;
                case AdPositionSDK.Top:
                    return S2sSdkBase.BannerPosition.TopCenter;
                case AdPositionSDK.TopRight:
                    return S2sSdkBase.BannerPosition.TopRight;
                case AdPositionSDK.CenterLeft:
                    return S2sSdkBase.BannerPosition.CenterLeft;
                case AdPositionSDK.Center:
                    return S2sSdkBase.BannerPosition.Centered;
                case AdPositionSDK.CenterRight:
                    return S2sSdkBase.BannerPosition.CenterRight;
                case AdPositionSDK.BottomLeft:
                    return S2sSdkBase.BannerPosition.BottomLeft;
                case AdPositionSDK.Bottom:
                    return S2sSdkBase.BannerPosition.BottomCenter;
                case AdPositionSDK.BottomRight:
                    return S2sSdkBase.BannerPosition.BottomRight;
                default:
                    return S2sSdkBase.BannerPosition.BottomCenter;
            }
        }

        protected override void CreateBannerView(bool isAdaptive, bool isCollapsible, bool isAdjustSizeForTablet = true, bool isBackgroundTransparent = true, AdPositionSDK position = AdPositionSDK.None, int offsetX = 1000000, int offsetY = 1000000)
        {
            if (SkipCreateBannerView(isAdaptive, isCollapsible)) return;

            if (IsCreated) return;
            IsCreated = true;

            if (position != AdPositionSDK.None)
            {
                CurrentPosition = position;
            }

            S2sSdkBase.BannerSize bannerSize = S2sSdkBase.BannerSize.Size_320x50;

            bool isTablet = DSDK.Extensions.DeviceTypeChecker.IsTablet();

            if (isAdjustSizeForTablet && isTablet)
            {
                BannerSize = new Vector2(728f, 90f);

                bannerSize = S2sSdkBase.BannerSize.Size_728x90;
            }
            else
            {
                BannerSize = new Vector2(320f, 50f);
            }

            AddCallback();

            S2sSdk.CreateBannerAd(AdUnitId, bannerSize, GetBannerPosition(CurrentPosition));
        }

        protected override void LoadInternal()
        {
            if (!IsCreated)
            {
                Create();
            }

            PluginRenderers.PrepareAdRequest();

            S2sSdk.SetBannerAdImpExtraParameter(AdUnitId, S2sSdkBase.BID_FLOOR_KEY, FloorValue);
            S2sSdk.SetBannerAdImpExtraParameter(AdUnitId, S2sSdkBase.BID_FLOOR_CUR_KEY, FloorCurrencyCode);

            S2sSdk.LoadBannerAd(AdUnitId);

            OnCallbackLoadRequest();
        }

        public override void Show(bool expand = false, AdPositionSDK position = AdPositionSDK.None, int offsetX = 1000000, int offsetY = 1000000)
        {
            // does not have collapsible banner
            if (SkipShow()) return;
            OnCallbackShowRequest();
            if (IsCreated)
            {
                if (position != AdPositionSDK.None && CurrentPosition != position)
                {
                    CurrentPosition = position;
                    Destroy();
                    Load();
                }
                else
                {
                    OnCallbackShowing();
                    S2sSdk.ShowBannerAd(AdUnitId);
                }
            }
            else
            {
                if (position != AdPositionSDK.None && CurrentPosition != position)
                {
                    CurrentPosition = position;
                }

                Load();
            }
        }

        public override void Hide()
        {
            if (SkipHide()) return;

            if (IsLoadSuccess())
            {
                S2sSdk.HideBannerAd(AdUnitId);
            }

            OnCallbackClosed();
        }

        public override void Destroy()
        {
            if (SkipDestroy()) return;

            S2sSdk.DestroyBannerAd(AdUnitId);
            RemoveCallback();
            OnCallbackDestroy();

            IsCreated = false;
        }
        #region Event Handlers
        private void AddCallback()
        {
            S2sSdkCallbacks.OnRequestSuccessEvent += OnRequestSuccessEvent;
            S2sSdkCallbacks.OnRequestFailedEvent += OnRequestFailedEvent;

            S2sSdkCallbacks.Banner.OnAdLoadedEvent += OnBannerLoadedEvent;
            S2sSdkCallbacks.Banner.OnAdLoadFailedEvent += OnBannerLoadFailedEvent;
            S2sSdkCallbacks.Banner.OnAdClickedEvent += OnBannerClickedEvent;

            S2sSdkCallbacks.Banner.OnAdDisplayedEvent += OnBannerRevenuePaidEvent;
        }

        private void RemoveCallback()
        {
            S2sSdkCallbacks.OnRequestSuccessEvent -= OnRequestSuccessEvent;
            S2sSdkCallbacks.OnRequestFailedEvent -= OnRequestFailedEvent;

            S2sSdkCallbacks.Banner.OnAdLoadedEvent -= OnBannerLoadedEvent;
            S2sSdkCallbacks.Banner.OnAdLoadFailedEvent -= OnBannerLoadFailedEvent;
            S2sSdkCallbacks.Banner.OnAdClickedEvent -= OnBannerClickedEvent;

            S2sSdkCallbacks.Banner.OnAdDisplayedEvent -= OnBannerRevenuePaidEvent;
        }

        private void OnRequestSuccessEvent(string adUnitId, S2sSdkBase.AuctionResult auctionResult)
        {
            if (adUnitId == AdUnitId)
            {
                OnCallbackRequestSuccess(0, auctionResult.DemandSource);
            }
        }

        private void OnRequestFailedEvent(string adUnitId, S2sSdkBase.ErrorInfo error)
        {
            if (adUnitId == AdUnitId)
            {
                OnCallbackRequestFailed(error.Message, 0);
            }
        }

        void OnBannerLoadedEvent(S2sSdkBase.AdInfo adInfo)
        {
            AdNetwork = adInfo.DemandSource;
            Revenue = adInfo.PriceValue;
            OnCallbackLoadSuccess(0);
        }

        void OnBannerLoadFailedEvent(S2sSdkBase.ErrorInfo errorInfo)
        {
            //AdNetwork = "ironSource";
            OnCallbackLoadFailure(errorInfo.Message, 0);
        }

        void OnBannerClickedEvent(S2sSdkBase.AdInfo adInfo)
        {
            AdNetwork = adInfo.DemandSource;
            OnCallbackClicked();
        }

        void OnBannerDisplayedEvent(S2sSdkBase.AdInfo adInfo)
        {
            AdNetwork = adInfo.DemandSource;
            OnCallbackShowSuccess();
        }

        void OnBannerDisplayFailedEvent(S2sSdkBase.ErrorInfo errorInfo)
        {
            //AdNetwork = errorInfo.DemandSource;
            OnCallbackShowFailure(errorInfo.Message);
        }

        void OnBannerCollapsedEvent(S2sSdkBase.AdInfo adInfo)
        {
            AdNetwork = adInfo.DemandSource;
            OnCallbackCollapsed();
        }

        void OnBannerExpandedEvent(S2sSdkBase.AdInfo adInfo)
        {
            AdNetwork = adInfo.DemandSource;
            OnCallbackExpanded();
        }

        private void OnBannerRevenuePaidEvent(S2sSdkBase.AdInfo adInfo)
        {
            AdNetwork = adInfo.DemandSource;

            double uplift = adInfo.PriceValue - BannerControl.Instance.GetRevenue(DSDK.Mediation.BannerID.default_banner);
            if (uplift < 0)
            {
                uplift = 0;
            }

            OnCallbackOnPaid(revenue: adInfo.PriceValue, adNetwork: adInfo.DemandSource, adUnit: adInfo.AdUnitId, uplift: uplift);
            Revenue = 0;
        }

        #endregion

        #region Helper Methods

        #endregion
    }
}