using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class TabControl : MonoBehaviour
{
    [SerializeField] private TabGroup _tabGroup;
    [SerializeField] private GameObject _tabContent;

    private void Awake()
    {
        if (_tabGroup != null)
        {
            _tabGroup.AddTabControl(this);
        }
    }

    public void OnTabSelected()
    {
        if (_tabContent == null) return;
        if (_tabGroup == null)
        {
            _tabContent.SetActive(true);
        }
        else
        {
            _tabGroup.OnTabSelected(this);
        }
    }

    public void Show()
    {
        _tabContent.SetActive(true);
    }

    public void Hide()
    {
        _tabContent.SetActive(false);
    }
}
