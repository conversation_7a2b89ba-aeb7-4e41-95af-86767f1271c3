using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class TabGroup : MonoBehaviour
{
    private List<TabControl> _tabControls = new List<TabControl>();

    public void AddTabControl(TabControl tabControl)
    {
        _tabControls.Add(tabControl);
    }

    public void OnTabSelected(TabControl tabControl)
    {
        foreach (var tab in _tabControls)
        {
            if (tab == tabControl)
            {
                tab.Show();
            }
            else
            {
                tab.Hide();
            }
        }
    }
}
