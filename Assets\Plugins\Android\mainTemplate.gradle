apply plugin: 'com.android.library'
**APPLY_PLUGINS**

configurations.all {
    exclude group: 'com.ironsource.sdk', module: 'mediationsdk'
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
// Android Resolver Dependencies Start
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4' // Packages/com.dmobin.google.ads.mobile/GoogleMobileAds/Editor/GoogleMobileAdsDependencies.xml:12
    implementation 'androidx.lifecycle:lifecycle-process:2.4.1' // Packages/com.dmobin.yandexmobileads.lite/Editor/YandexMobileadsDependencies.xml:12
    implementation 'androidx.recyclerview:recyclerview:1.2.1' // Assets/LevelPlay/Editor/ISMintegralAdapterDependencies.xml:16
    implementation 'com.adjust.sdk:adjust-android:5.1.0' // Packages/com.dmobin.adjust/Native/Editor/Dependencies.xml:5
    implementation 'com.android.installreferrer:installreferrer:2.2' // Packages/com.dmobin.adjust/Native/Editor/Dependencies.xml:7
    implementation 'com.android.support:appcompat-v7:25.3.1' // Facebook.Unity.Editor.AndroidSupportLibraryResolver.addSupportLibraryDependency
    implementation 'com.android.support:cardview-v7:25.3.1' // Facebook.Unity.Editor.AndroidSupportLibraryResolver.addSupportLibraryDependency
    implementation 'com.android.support:customtabs:25.3.1' // Facebook.Unity.Editor.AndroidSupportLibraryResolver.addSupportLibraryDependency
    implementation 'com.android.support:support-v4:25.3.1' // Facebook.Unity.Editor.AndroidSupportLibraryResolver.addSupportLibraryDependency
    implementation 'com.applovin:applovin-sdk:13.0.0' // Packages/com.applovin.mediation.ads/MaxSdk/AppLovin/Editor/Dependencies.xml:4
    implementation 'com.bigossp:bigo-ads:5.0.2' // Assets/LevelPlay/Editor/ISBigoAdapterDependencies.xml:10
    implementation 'com.facebook.android:audience-network-sdk:6.18.0' // Packages/com.dmobin.costcenter.mediation.s2sbidding/S2sPluginRenderers/Editor/Dependencies.xml:10
    implementation 'com.facebook.android:facebook-applinks:[18.0.0,19)' // Packages/com.dmobin.facebook/FacebookSDK/Plugins/Editor/Dependencies.xml:6
    implementation 'com.facebook.android:facebook-core:[18.0.0,19)' // Packages/com.dmobin.facebook/FacebookSDK/Plugins/Editor/Dependencies.xml:5
    implementation 'com.facebook.android:facebook-gamingservices:[18.0.0,19)' // Packages/com.dmobin.facebook/FacebookSDK/Plugins/Editor/Dependencies.xml:9
    implementation 'com.facebook.android:facebook-login:[18.0.0,19)' // Packages/com.dmobin.facebook/FacebookSDK/Plugins/Editor/Dependencies.xml:7
    implementation 'com.facebook.android:facebook-share:[18.0.0,19)' // Packages/com.dmobin.facebook/FacebookSDK/Plugins/Editor/Dependencies.xml:8
    implementation 'com.fyber:marketplace-sdk:8.3.5' // Assets/LevelPlay/Editor/ISFyberAdapterDependencies.xml:9
    implementation 'com.github.bumptech.glide:glide:4.16.0' // Packages/com.dmobin.sdk.nativeads/Editor/Android/GDKNativeDependencies.xml:3
    implementation 'com.google.ads.mediation:facebook:6.18.0.0' // Assets/GoogleMobileAds/Mediation/MetaAudienceNetwork/Editor/MetaAudienceNetworkMediationDependencies.xml:24
    implementation 'com.google.ads.mediation:pangle:6.4.0.6.0' // Assets/GoogleMobileAds/Mediation/Pangle/Editor/PangleMediationDependencies.xml:25
    implementation 'com.google.android.gms:play-services-ads:23.6.0' // Packages/com.dmobin.google.ads.mobile/GoogleMobileAds/Editor/GoogleMobileAdsDependencies.xml:7
    // implementation 'com.google.android.gms:play-services-ads-identifier:18.0.1' // Packages/com.dmobin.adjust/Native/Editor/AndroidDependencies.xml:20
    implementation 'com.google.android.gms:play-services-ads-identifier:18.1.0' // Assets/LevelPlay/Editor/IronSourceSDKDependencies.xml:12
    implementation 'com.google.android.gms:play-services-appset:16.0.2' // Packages/com.dmobin.adjust/Native/Editor/AndroidDependencies.xml:29
    implementation 'com.google.android.gms:play-services-base:18.5.0' // Packages/com.dmobin.google.firebase.app/Firebase/Editor/AppDependencies.xml:16
    implementation 'com.google.android.play:core-common:2.0.4' // Packages/com.google.play.core/Editor/Dependencies.xml:3
    implementation 'com.google.android.play:review:2.0.2' // Packages/com.google.play.review/Editor/Dependencies.xml:3
    implementation 'com.google.android.ump:user-messaging-platform:3.1.0' // Packages/com.dmobin.google.ads.mobile/GoogleMobileAds/Editor/GoogleUmpDependencies.xml:7
    implementation 'com.google.code.gson:gson:2.8.5' // Packages/com.dmobin.sdk.mediation.googlemobileads-native/GoogleMobileAdsNative/Editor/GoogleMobileAdsNativeDependencies.xml:7
    implementation 'com.google.firebase:firebase-analytics:22.1.2' // Packages/com.dmobin.google.firebase.remote-config/Firebase/Editor/RemoteConfigDependencies.xml:15
    implementation 'com.google.firebase:firebase-analytics-unity:12.5.0' // Packages/com.dmobin.google.firebase.analytics/Firebase/Editor/AnalyticsDependencies.xml:18
    implementation 'com.google.firebase:firebase-app-unity:12.5.0' // Packages/com.dmobin.google.firebase.app/Firebase/Editor/AppDependencies.xml:21
    implementation 'com.google.firebase:firebase-common:21.0.0' // Packages/com.dmobin.google.firebase.app/Firebase/Editor/AppDependencies.xml:12
    implementation 'com.google.firebase:firebase-config:22.0.1' // Packages/com.dmobin.google.firebase.remote-config/Firebase/Editor/RemoteConfigDependencies.xml:13
    implementation 'com.google.firebase:firebase-config-unity:12.5.0' // Packages/com.dmobin.google.firebase.remote-config/Firebase/Editor/RemoteConfigDependencies.xml:20
    implementation 'com.google.firebase:firebase-crashlytics-ndk:19.3.0' // Packages/com.dmobin.google.firebase.crashlytics/Firebase/Editor/CrashlyticsDependencies.xml:13
    implementation 'com.google.firebase:firebase-crashlytics-unity:12.5.0' // Packages/com.dmobin.google.firebase.crashlytics/Firebase/Editor/CrashlyticsDependencies.xml:20
    implementation 'com.google.firebase:firebase-iid:21.1.0' // Packages/com.dmobin.google.firebase.messaging/Firebase/Editor/MessagingDependencies.xml:17
    implementation 'com.google.firebase:firebase-messaging:24.1.0' // Packages/com.dmobin.google.firebase.messaging/Firebase/Editor/MessagingDependencies.xml:13
    implementation 'com.google.firebase:firebase-messaging-unity:12.5.0' // Packages/com.dmobin.google.firebase.messaging/Firebase/Editor/MessagingDependencies.xml:24
    implementation 'com.google.flatbuffers:flatbuffers-java:1.12.0' // Packages/com.dmobin.google.firebase.messaging/Firebase/Editor/MessagingDependencies.xml:19
    implementation 'com.knorex:knorex-sdk-plugin-renders:1.6.0' // Packages/com.dmobin.costcenter.mediation.s2sbidding/S2sPluginRenderers/Editor/Dependencies.xml:8
    implementation 'com.knorex:knorex-sdk-unity:1.6.0' // Packages/com.dmobin.costcenter.mediation.s2sbidding/S2sBiddingSdk/Editor/Dependencies.xml:8
    implementation 'com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.31' // Assets/LevelPlay/Editor/ISMintegralAdapterDependencies.xml:9
    implementation 'com.moloco.sdk:moloco-sdk:3.6.1' // Assets/LevelPlay/Editor/ISMolocoAdapterDependencies.xml:10
    implementation 'com.my.target:mytarget-sdk:5.27.1' // Assets/LevelPlay/Editor/ISMyTargetAdapterDependencies.xml:9
    implementation 'com.pangle.global:ads-sdk:6.4.0.6' // Assets/LevelPlay/Editor/ISPangleAdapterDependencies.xml:13
    implementation 'com.parse.bolts:bolts-android:1.4.0' // Packages/com.dmobin.facebook/FacebookSDK/Plugins/Editor/Dependencies.xml:4
    implementation 'com.unity3d.ads:unity-ads:4.13.1' // Assets/LevelPlay/Editor/ISUnityAdsAdapterDependencies.xml:13
    implementation 'com.unity3d.ads-mediation:admob-adapter:4.3.49' // Assets/LevelPlay/Editor/ISAdMobAdapterDependencies.xml:13
    implementation 'com.unity3d.ads-mediation:adquality-sdk:7.23.2' // Packages/com.dmobin.levelplay.adquality/Editor/IronSourceAdQualityDependencies.xml:5
    implementation 'com.unity3d.ads-mediation:adquality-unity-sdk-bridge:7.23.2' // Packages/com.dmobin.levelplay.adquality/Editor/IronSourceAdQualityDependencies.xml:10
    implementation 'com.unity3d.ads-mediation:applovin-adapter:4.3.47' // Assets/LevelPlay/Editor/ISAppLovinAdapterDependencies.xml:6
    implementation 'com.unity3d.ads-mediation:bidmachine-adapter:4.3.14' // Assets/LevelPlay/Editor/ISBidMachineAdapterDependencies.xml:5
    implementation 'com.unity3d.ads-mediation:bigo-adapter:4.3.5' // Assets/LevelPlay/Editor/ISBigoAdapterDependencies.xml:6
    implementation 'com.unity3d.ads-mediation:facebook-adapter:4.3.48' // Assets/LevelPlay/Editor/ISFacebookAdapterDependencies.xml:13
    implementation 'com.unity3d.ads-mediation:fyber-adapter:4.3.36' // Assets/LevelPlay/Editor/ISFyberAdapterDependencies.xml:13
    implementation 'com.unity3d.ads-mediation:mediation-sdk:8.6.1' // Assets/LevelPlay/Editor/IronSourceSDKDependencies.xml:5
    implementation 'com.unity3d.ads-mediation:mintegral-adapter:4.3.35' // Assets/LevelPlay/Editor/ISMintegralAdapterDependencies.xml:20
    implementation 'com.unity3d.ads-mediation:moloco-adapter:4.3.12' // Assets/LevelPlay/Editor/ISMolocoAdapterDependencies.xml:6
    implementation 'com.unity3d.ads-mediation:mytarget-adapter:4.3.0' // Assets/LevelPlay/Editor/ISMyTargetAdapterDependencies.xml:5
    implementation 'com.unity3d.ads-mediation:pangle-adapter:4.3.34' // Assets/LevelPlay/Editor/ISPangleAdapterDependencies.xml:6
    implementation 'com.unity3d.ads-mediation:unityads-adapter:4.3.49' // Assets/LevelPlay/Editor/ISUnityAdsAdapterDependencies.xml:6
    implementation 'com.unity3d.ads-mediation:vungle-adapter:4.3.29' // Assets/LevelPlay/Editor/ISVungleAdapterDependencies.xml:13
    implementation 'com.unity3d.ads-mediation:yandex-adapter:4.3.7' // Assets/LevelPlay/Editor/ISYandexAdapterDependencies.xml:10
    implementation 'com.vungle:vungle-ads:7.4.3' // Assets/LevelPlay/Editor/ISVungleAdapterDependencies.xml:9
    implementation 'com.yandex.android:mobileads:7.8.0' // Packages/com.dmobin.yandexmobileads.lite/Editor/YandexMobileadsDependencies.xml:7
    implementation 'io.bidmachine:ads:3.3.0' // Assets/LevelPlay/Editor/ISBidMachineAdapterDependencies.xml:12
    implementation 'jp.wasabeef:blurry:4.0.1' // Packages/com.dmobin.sdk.nativeads/Editor/Android/GDKNativeDependencies.xml:4
// Android Resolver Dependencies End
**DEPS**}

// Android Resolver Exclusions Start
android {
  packagingOptions {
      exclude ('/lib/armeabi/*' + '*')
      exclude ('/lib/mips/*' + '*')
      exclude ('/lib/mips64/*' + '*')
      exclude ('/lib/x86/*' + '*')
      exclude ('/lib/x86_64/*' + '*')
  }
}
// Android Resolver Exclusions End
android {
    ndkPath "**NDKPATH**"

    compileSdkVersion **APIVERSION**
    buildToolsVersion '**BUILDTOOLS**'

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    defaultConfig {
        minSdkVersion **MINSDKVERSION**
        targetSdkVersion **TARGETSDKVERSION**
        ndk {
            abiFilters **ABIFILTERS**
        }
        versionCode **VERSIONCODE**
        versionName '**VERSIONNAME**'
        consumerProguardFiles 'proguard-unity.txt'**USER_PROGUARD**
    }

    lintOptions {
        abortOnError false
    }

    aaptOptions {
        noCompress = **BUILTIN_NOCOMPRESS** + unityStreamingAssets.tokenize(', ')
        ignoreAssetsPattern = "!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"
    }**PACKAGING_OPTIONS**
}
**IL_CPP_BUILD_SETUP**
**SOURCE_BUILD_SETUP**
**EXTERNAL_SOURCES**
